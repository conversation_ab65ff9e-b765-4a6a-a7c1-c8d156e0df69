/**
 * Multi-Account Management Service
 * Handles account creation, validation, and management with 4-account limit
 */

import { supabase } from '@/lib/supabase/client'
import type { TradingAccount, TradingAccountInsert, Challenge } from '@/types/database'

interface AccountCreationResult {
  success: boolean
  message: string
  accountId?: string
  account?: TradingAccount
}

interface AccountValidationResult {
  canCreateAccount: boolean
  currentAccountCount: number
  maxAccounts: number
  availableSlots: number
  violations: string[]
}

export class MultiAccountService {
  private readonly MAX_FUNDED_ACCOUNTS = 4
  private readonly MAX_TOTAL_ACCOUNTS = 10 // Including challenge accounts

  /**
   * Validate if user can create a new account
   */
  async validateAccountCreation(userId: string, accountType: 'challenge' | 'funded'): Promise<AccountValidationResult> {
    try {
      // Get all user accounts
      const { data: accounts, error } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)
        .neq('status', 'closed')

      if (error) {
        throw new Error('Failed to fetch user accounts')
      }

      const violations: string[] = []
      const totalAccounts = accounts?.length || 0
      // Use phase instead of account_type (funded accounts have phase 'funded', others are challenges)
      const fundedAccounts = accounts?.filter(acc => acc.phase === 'funded').length || 0
      const challengeAccounts = accounts?.filter(acc => acc.phase !== 'funded').length || 0

      // Check limits based on account type
      if (accountType === 'funded') {
        if (fundedAccounts >= this.MAX_FUNDED_ACCOUNTS) {
          violations.push(`Maximum funded accounts reached (${this.MAX_FUNDED_ACCOUNTS})`)
        }
      } else {
        // For challenge accounts, check total limit
        if (totalAccounts >= this.MAX_TOTAL_ACCOUNTS) {
          violations.push(`Maximum total accounts reached (${this.MAX_TOTAL_ACCOUNTS})`)
        }
      }

      // Check for active challenge accounts (user shouldn't have multiple active challenges)
      if (accountType === 'challenge') {
        const activeChallenges = accounts?.filter(acc => 
          acc.account_type === 'challenge' && 
          acc.status === 'active' && 
          acc.phase !== 'failed'
        ).length || 0

        if (activeChallenges >= 3) { // Allow up to 3 concurrent challenges
          violations.push('Maximum concurrent challenges reached (3)')
        }
      }

      const canCreateAccount = violations.length === 0
      const availableSlots = accountType === 'funded' 
        ? this.MAX_FUNDED_ACCOUNTS - fundedAccounts
        : this.MAX_TOTAL_ACCOUNTS - totalAccounts

      return {
        canCreateAccount,
        currentAccountCount: accountType === 'funded' ? fundedAccounts : totalAccounts,
        maxAccounts: accountType === 'funded' ? this.MAX_FUNDED_ACCOUNTS : this.MAX_TOTAL_ACCOUNTS,
        availableSlots: Math.max(0, availableSlots),
        violations
      }
    } catch (error) {
      console.error('Error validating account creation:', error)
      return {
        canCreateAccount: false,
        currentAccountCount: 0,
        maxAccounts: 0,
        availableSlots: 0,
        violations: ['Failed to validate account creation']
      }
    }
  }

  /**
   * Create a new challenge account
   */
  async createChallengeAccount(userId: string, challengeId: string, paymentId?: string): Promise<AccountCreationResult> {
    try {
      // Validate account creation
      const validation = await this.validateAccountCreation(userId, 'challenge')
      if (!validation.canCreateAccount) {
        return {
          success: false,
          message: validation.violations.join(', ')
        }
      }

      // Get challenge details
      const { data: challenge, error: challengeError } = await supabase
        .from('challenges')
        .select('*')
        .eq('id', challengeId)
        .single()

      if (challengeError || !challenge) {
        return {
          success: false,
          message: 'Challenge not found'
        }
      }

      // For now, all accounts are treated equally (no primary account concept)
      const isPrimaryAccount = false

      // Generate unique account number
      const accountNumber = await this.generateAccountNumber('CHG')

      // Create challenge account (using only existing database columns)
      const accountData: any = {
        user_id: userId,
        challenge_id: challengeId,
        account_number: accountNumber,
        balance: challenge.price * 100, // Challenge balance based on price tier
        equity: challenge.price * 100,
        free_margin: challenge.price * 100,
        margin_level: 100,
        drawdown: 0,
        max_drawdown: 0,
        daily_loss: 0,
        total_loss: 0,
        profit: 0,
        phase: 'evaluation',
        status: 'active',
        mt5_login: accountNumber,
        mt5_password: 'DemoPass123',
        mt5_server: 'demo.propfirm.com:443',
        start_date: new Date().toISOString()
      }

      const { data: newAccount, error: accountError } = await supabase
        .from('trading_accounts')
        .insert(accountData)
        .select()
        .single()

      if (accountError || !newAccount) {
        return {
          success: false,
          message: 'Failed to create challenge account'
        }
      }

      // Log account creation
      await this.logAccountEvent(newAccount.id, 'account_created', {
        accountType: 'challenge',
        challengeId,
        paymentId,
        isPrimary: isPrimaryAccount
      })

      return {
        success: true,
        message: 'Challenge account created successfully',
        accountId: newAccount.id,
        account: newAccount
      }
    } catch (error) {
      console.error('Error creating challenge account:', error)
      return {
        success: false,
        message: 'Internal server error'
      }
    }
  }

  /**
   * Create a new funded account (called after passing challenge)
   */
  async createFundedAccount(
    userId: string, 
    challengeId: string, 
    fundedAmount: number,
    sourceAccountId?: string
  ): Promise<AccountCreationResult> {
    try {
      // Validate account creation
      const validation = await this.validateAccountCreation(userId, 'funded')
      if (!validation.canCreateAccount) {
        return {
          success: false,
          message: validation.violations.join(', ')
        }
      }

      // Check if user has primary funded account
      const { data: existingFundedAccounts } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('phase', 'funded')

      const isPrimaryAccount = !existingFundedAccounts || existingFundedAccounts.length === 0

      // Generate unique account number
      const accountNumber = await this.generateAccountNumber('FUND')
      
      // Calculate next payout date (14 days from now by default)
      const nextPayoutDate = new Date()
      nextPayoutDate.setDate(nextPayoutDate.getDate() + 14)

      // Generate nickname based on account count
      const { data: userFundedAccounts } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('phase', 'funded')

      const accountCount = (userFundedAccounts?.length || 0) + 1
      const nickname = isPrimaryAccount 
        ? 'Primary Funded Account'
        : `Funded Account ${accountCount}`

      // Create funded account
      const accountData: TradingAccountInsert = {
        user_id: userId,
        challenge_id: challengeId,
        account_number: accountNumber,
        account_type: 'funded',
        balance: fundedAmount,
        equity: fundedAmount,
        phase: 'funded',
        status: 'active',
        is_simulated: true,
        simulated_capital: fundedAmount,
        payout_frequency: 14,
        payout_percentage: 14.0,
        next_payout_date: nextPayoutDate.toISOString(),
        accumulated_profit: 0,
        // is_primary: isPrimaryAccount, // Column doesn't exist in database
        account_nickname: nickname,
        copy_trading_enabled: false,
        start_date: new Date().toISOString()
      }

      const { data: newAccount, error: accountError } = await supabase
        .from('trading_accounts')
        .insert(accountData)
        .select()
        .single()

      if (accountError || !newAccount) {
        return {
          success: false,
          message: 'Failed to create funded account'
        }
      }

      // Log account creation
      await this.logAccountEvent(newAccount.id, 'funded_account_created', {
        fundedAmount,
        sourceAccountId,
        isPrimary: isPrimaryAccount,
        payoutFrequency: 14
      })

      return {
        success: true,
        message: 'Funded account created successfully',
        accountId: newAccount.id,
        account: newAccount
      }
    } catch (error) {
      console.error('Error creating funded account:', error)
      return {
        success: false,
        message: 'Internal server error'
      }
    }
  }

  /**
   * Set primary account for user
   */
  async setPrimaryAccount(userId: string, accountId: string): Promise<{ success: boolean; message: string }> {
    // Temporarily disabled - database doesn't have is_primary column
    return { success: true, message: 'Primary account functionality disabled' }
    
    try {
      // Verify account belongs to user
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single()

      if (accountError || !account) {
        return { success: false, message: 'Account not found' }
      }

      // Remove primary status from all user accounts
      await supabase
        .from('trading_accounts')
        // .update({ is_primary: false }) // Column doesn't exist
        .eq('user_id', userId)

      // Set new primary account
      const { error: updateError } = await supabase
        .from('trading_accounts')
        // .update({ is_primary: true }) // Column doesn't exist
        .eq('id', accountId)

      if (updateError) {
        return { success: false, message: 'Failed to set primary account' }
      }

      // Log the change
      await this.logAccountEvent(accountId, 'primary_account_changed', {
        newPrimaryAccountId: accountId
      })

      return { success: true, message: 'Primary account updated successfully' }
    } catch (error) {
      console.error('Error setting primary account:', error)
      return { success: false, message: 'Internal server error' }
    }
  }

  /**
   * Close an account
   */
  async closeAccount(userId: string, accountId: string, reason: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify account belongs to user
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('id', accountId)
        .eq('user_id', userId)
        .single()

      if (accountError || !account) {
        return { success: false, message: 'Account not found' }
      }

      // Don't allow closing account with open positions
      const { data: openTrades } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', accountId)
        .eq('status', 'open')

      if (openTrades && openTrades.length > 0) {
        return { 
          success: false, 
          message: 'Cannot close account with open positions. Please close all trades first.' 
        }
      }

      // Close the account
      const { error: closeError } = await supabase
        .from('trading_accounts')
        .update({
          status: 'closed',
          end_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', accountId)

      if (closeError) {
        return { success: false, message: 'Failed to close account' }
      }

      // Skip primary account logic since column doesn't exist
      // if (account.is_primary) {
      //   // Primary account logic would go here
      // }

      // Log account closure
      await this.logAccountEvent(accountId, 'account_closed', {
        reason,
        wasPrimary: false // Set to false since is_primary doesn't exist
      })

      return { success: true, message: 'Account closed successfully' }
    } catch (error) {
      console.error('Error closing account:', error)
      return { success: false, message: 'Internal server error' }
    }
  }

  /**
   * Get user account summary
   */
  async getUserAccountSummary(userId: string): Promise<{
    totalAccounts: number
    fundedAccounts: number
    challengeAccounts: number
    activeAccounts: number
    closedAccounts: number
    totalBalance: number
    totalEquity: number
    availableSlots: number
    maxAccounts: number
  }> {
    try {
      const { data: accounts } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)

      const accountList = accounts || []
      
      // Determine account types based on existing schema
      // Challenge accounts have challenge_id and phase = 'evaluation'
      // Funded accounts have phase = 'funded' or 'live' 
      const challengeAccounts = accountList.filter(acc => acc.challenge_id && (acc.phase === 'evaluation' || acc.phase === 'verification'))
      const fundedAccounts = accountList.filter(acc => acc.phase === 'funded' || acc.phase === 'live')
      
      return {
        totalAccounts: accountList.length,
        fundedAccounts: fundedAccounts.length,
        challengeAccounts: challengeAccounts.length,
        activeAccounts: accountList.filter(acc => acc.status === 'active').length,
        closedAccounts: accountList.filter(acc => acc.status === 'closed').length,
        totalBalance: accountList.reduce((sum, acc) => sum + (acc.balance || 0), 0),
        totalEquity: accountList.reduce((sum, acc) => sum + (acc.equity || 0), 0),
        availableSlots: Math.max(0, this.MAX_FUNDED_ACCOUNTS - fundedAccounts.length),
        maxAccounts: this.MAX_FUNDED_ACCOUNTS
      }
    } catch (error) {
      console.error('Error getting user account summary:', error)
      return {
        totalAccounts: 0,
        fundedAccounts: 0,
        challengeAccounts: 0,
        activeAccounts: 0,
        closedAccounts: 0,
        totalBalance: 0,
        totalEquity: 0,
        availableSlots: 0,
        maxAccounts: this.MAX_FUNDED_ACCOUNTS
      }
    }
  }

  /**
   * Generate unique account number
   */
  private async generateAccountNumber(prefix: string): Promise<string> {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 6).toUpperCase()
    return `${prefix}-${timestamp}-${random}`
  }

  /**
   * Log account events for audit trail
   */
  private async logAccountEvent(accountId: string, event: string, data: any): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          resource_type: 'trading_account',
          resource_id: accountId,
          action: event,
          new_values: data
        })
    } catch (error) {
      console.error('Failed to log account event:', error)
    }
  }
}

// Export singleton instance
export const multiAccountService = new MultiAccountService()