import axios from 'axios'
import { getTradingViewWebSocketClient, TradingViewCandle } from './tradingViewWebSocketClient'

// Market data interfaces
export interface MarketQuote {
  symbol: string
  bid: number
  ask: number
  last: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  timestamp: Date
}

export interface CandleData {
  symbol: string
  timeframe: string
  open: number
  high: number
  low: number
  close: number
  volume: number
  timestamp: Date
}

// API client interfaces
interface AlphaVantageQuote {
  '01. symbol': string
  '02. open': string
  '03. high': string
  '04. low': string
  '05. price': string
  '06. volume': string
  '07. latest trading day': string
  '08. previous close': string
  '09. change': string
  '10. change percent': string
}

interface FinnhubQuote {
  c: number // current price
  d: number // change
  dp: number // percent change
  h: number // high
  l: number // low
  o: number // open
  pc: number // previous close
  t: number // timestamp
}

export class MarketDataService {
  private alphaVantageKey: string
  private finnhubKey: string
  private polygonKey: string
  private cache: Map<string, { data: MarketQuote; timestamp: number }> = new Map()
  private cacheTimeout = 2000 // 2 seconds cache
  private tradingViewFailureCount = 0
  private maxTradingViewFailures = 5

  constructor() {
    this.alphaVantageKey = process.env.ALPHA_VANTAGE_API_KEY || ''
    this.finnhubKey = process.env.FINNHUB_API_KEY || ''
    this.polygonKey = process.env.POLYGON_API_KEY || ''
  }

  private getCacheKey(symbol: string): string {
    return `quote_${symbol.toUpperCase()}`
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout
  }

  private getCachedQuote(symbol: string): MarketQuote | null {
    const key = this.getCacheKey(symbol)
    const cached = this.cache.get(key)
    
    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data
    }
    
    return null
  }

  private setCachedQuote(symbol: string, quote: MarketQuote): void {
    const key = this.getCacheKey(symbol)
    this.cache.set(key, {
      data: quote,
      timestamp: Date.now()
    })
  }

  // Generate mock data for development to avoid API rate limits
  private getMockQuote(symbol: string): MarketQuote {
    // Generate realistic but fake data
    const basePrice = this.getMockBasePrice(symbol)
    const changePercent = (Math.random() - 0.5) * 4 // -2% to +2%
    const change = basePrice * (changePercent / 100)
    const spread = basePrice * 0.0001 // 0.01% spread
    
    return {
      symbol,
      bid: basePrice - spread / 2,
      ask: basePrice + spread / 2,
      last: basePrice,
      change,
      changePercent,
      volume: Math.floor(Math.random() * 1000000000),
      high: basePrice + Math.abs(change) * 1.5,
      low: basePrice - Math.abs(change) * 1.5,
      timestamp: new Date()
    }
  }

  // Get realistic base prices for different symbols
  private getMockBasePrice(symbol: string): number {
    const mockPrices: { [key: string]: number } = {
      'EUR/USD': 1.0847,
      'EURUSD=X': 1.0847,
      'GBP/USD': 1.2634,
      'GBPUSD=X': 1.2634,
      'USD/JPY': 149.82,
      'USDJPY=X': 149.82,
      'USD/CHF': 0.8934,
      'USDCHF=X': 0.8934,
      'AUD/USD': 0.6789,
      'AUDUSD=X': 0.6789,
      'USD/CAD': 1.3456,
      'USDCAD=X': 1.3456,
      'NZD/USD': 0.6234,
      'NZDUSD=X': 0.6234,
      'GOLD': 2045.30,
      'GC=F': 2045.30,
      'SILVER': 24.87,
      'SI=F': 24.87,
      'OIL': 74.85,
      'CL=F': 74.85,
      'NATGAS': 2.89,
      'SPX500': 4567.89,
      'US30': 34567.12,
      'NAS100': 15234.56,
      'UK100': 7234.45,
      'BTCUSD': 43250,
      'BINANCE:BTCUSDT': 43250,
      'ETHUSD': 2567.34,
      'BINANCE:ETHUSDT': 2567.34,
      'XRPUSD': 0.6789,
      'COINBASE:XRPUSD': 2.8600
    }
    
    return mockPrices[symbol] || 100 + Math.random() * 900 // Default random price
  }

  // Get live quote from Revolutionary WebSocket Service (Primary method - 140+ symbols)
  async getRevolutionaryQuote(symbol: string): Promise<MarketQuote | null> {
    try {
      // Check cache first
      const cached = this.getCachedQuote(symbol)
      if (cached) return cached

      const revolutionaryUrl = `${process.env.REVOLUTIONARY_SERVICE_URL || 'http://localhost:3002'}/api/quote?symbol=${symbol}`
      const response = await axios.get(revolutionaryUrl, { 
        timeout: 8000, // Increased timeout for Revolutionary Service
        headers: {
          'User-Agent': 'PropFirm-Backend/1.0.0'
        }
      })
      
      if (response.data && response.data.data) {
        const quoteData = response.data.data
        
        const marketQuote: MarketQuote = {
          symbol,
          bid: quoteData.bid || quoteData.lp - 0.0001, // Use bid or estimate from last price
          ask: quoteData.ask || quoteData.lp + 0.0001, // Use ask or estimate from last price
          last: quoteData.lp || quoteData.price,
          change: quoteData.ch || 0,
          changePercent: quoteData.chp || 0,
          volume: quoteData.volume || 0,
          high: quoteData.high || quoteData.lp, // Revolutionary service may not provide all fields
          low: quoteData.low || quoteData.lp,
          timestamp: new Date(response.data.timestamp || Date.now())
        }

        this.setCachedQuote(symbol, marketQuote)
        console.log(`✅ Revolutionary quote retrieved for ${symbol}: $${marketQuote.last}`)
        return marketQuote
      }

      console.warn(`⚠️ Revolutionary Service returned empty data for ${symbol}`)
      return null

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          console.error(`⏰ Revolutionary Service timeout for ${symbol} - service may be overloaded`)
        } else if (error.code === 'ECONNREFUSED') {
          console.error(`🔌 Revolutionary Service connection refused for ${symbol} - service not running?`)
        } else if (error.response?.status === 500) {
          console.error(`💥 Revolutionary Service internal error for ${symbol}:`, error.response.data?.details || error.response.data?.error)
        } else {
          console.error(`❌ Revolutionary Service error for ${symbol}:`, error.message)
        }
      } else {
        console.error(`Revolutionary Service error for ${symbol}:`, error)
      }
      return null
    }
  }

  // Get all 140+ quotes from Revolutionary Service (using individual symbol requests)
  async getAllRevolutionaryQuotes(symbols?: string[]): Promise<Record<string, MarketQuote>> {
    try {
      // If no symbols provided, use a common set of symbols
      const targetSymbols = symbols || [
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
        'BTCUSD', 'ETHUSD', 'ADAUSD', 'XRPUSD',
        'AAPL', 'TSLA', 'GOOGL', 'MSFT', 'AMZN',
        'SPX500', 'US30', 'NAS100', 'UK100',
        'GOLD', 'SILVER', 'CRUDE_OIL', 'NATURAL_GAS'
      ]

      const marketQuotes: Record<string, MarketQuote> = {}
      const promises = targetSymbols.map(async (symbol) => {
        const quote = await this.getRevolutionaryQuote(symbol)
        if (quote) {
          marketQuotes[symbol] = quote
        }
      })

      await Promise.allSettled(promises) // Use allSettled to handle individual failures gracefully

      console.log(`📊 Fetched ${Object.keys(marketQuotes).length}/${targetSymbols.length} quotes from Revolutionary Service`)
      return marketQuotes

    } catch (error) {
      console.error('Error fetching all revolutionary quotes:', error)
      return {}
    }
  }

  // Get live quote from Alpha Vantage (Fallback method)
  async getAlphaVantageQuote(symbol: string): Promise<MarketQuote | null> {
    try {
      // Check cache first
      const cached = this.getCachedQuote(symbol)
      if (cached) return cached

      // In development, return mock data to avoid API rate limits
      if (process.env.NODE_ENV === 'development') {
        return this.getMockQuote(symbol)
      }

      const response = await axios.get('https://www.alphavantage.co/query', {
        params: {
          function: 'GLOBAL_QUOTE',
          symbol,
          apikey: this.alphaVantageKey
        },
        timeout: 5000
      })

      const quote = response.data['Global Quote'] as AlphaVantageQuote
      if (!quote || !quote['05. price']) {
        return null
      }

      const price = parseFloat(quote['05. price'])
      const change = parseFloat(quote['09. change'])
      const changePercent = parseFloat(quote['10. change percent'].replace('%', ''))
      const volume = parseInt(quote['06. volume'])

      const marketQuote: MarketQuote = {
        symbol: quote['01. symbol'],
        bid: price - 0.0001, // Approximate bid/ask spread
        ask: price + 0.0001,
        last: price,
        change,
        changePercent,
        volume,
        high: parseFloat(quote['03. high']),
        low: parseFloat(quote['04. low']),
        timestamp: new Date()
      }

      this.setCachedQuote(symbol, marketQuote)
      return marketQuote

    } catch (error) {
      console.error(`Error fetching Alpha Vantage quote for ${symbol}:`, error)
      return null
    }
  }

  // Get live quote from Finnhub (good for stocks, forex, crypto)
  async getFinnhubQuote(symbol: string): Promise<MarketQuote | null> {
    try {
      // Check cache first
      const cached = this.getCachedQuote(symbol)
      if (cached) return cached

      // In development, return mock data to avoid API rate limits
      if (process.env.NODE_ENV === 'development') {
        return this.getMockQuote(symbol)
      }

      const response = await axios.get('https://finnhub.io/api/v1/quote', {
        params: {
          symbol,
          token: this.finnhubKey
        },
        timeout: 5000
      })

      const quote = response.data as FinnhubQuote
      if (!quote || !quote.c) {
        return null
      }

      const marketQuote: MarketQuote = {
        symbol,
        bid: quote.c - 0.0001, // Approximate bid/ask spread
        ask: quote.c + 0.0001,
        last: quote.c,
        change: quote.d,
        changePercent: quote.dp,
        volume: 0, // Finnhub doesn't provide volume in quote endpoint
        high: quote.h,
        low: quote.l,
        timestamp: new Date(quote.t * 1000)
      }

      this.setCachedQuote(symbol, marketQuote)
      return marketQuote

    } catch (error) {
      console.error(`Error fetching Finnhub quote for ${symbol}:`, error)
      return null
    }
  }

  // Get forex quote with proper currency pair formatting
  async getForexQuote(pair: string): Promise<MarketQuote | null> {
    // Convert EUR/USD to EURUSD=X format for some APIs
    const symbol = pair.includes('/') ? pair.replace('/', '') + '=X' : pair
    
    // Try Finnhub first (faster), then Alpha Vantage as fallback
    let quote = await this.getFinnhubQuote(symbol)
    if (!quote) {
      quote = await this.getAlphaVantageQuote(pair)
    }
    
    return quote
  }

  // Get crypto quote
  async getCryptoQuote(symbol: string): Promise<MarketQuote | null> {
    // For crypto, use Finnhub with proper symbol format (e.g., BINANCE:BTCUSDT)
    const cryptoSymbol = symbol.includes(':') ? symbol : `BINANCE:${symbol}`
    return this.getFinnhubQuote(cryptoSymbol)
  }

  // Get stock quote
  async getStockQuote(symbol: string): Promise<MarketQuote | null> {
    return this.getFinnhubQuote(symbol)
  }

  // Get quote using enterprise API with fallbacks (Primary method)
  async getQuote(symbol: string): Promise<MarketQuote | null> {
    try {
      // Priority 1: Try Revolutionary WebSocket Service (140+ symbols, zero 429 errors)
      const revolutionaryQuote = await this.getRevolutionaryQuote(symbol)
      if (revolutionaryQuote) {
        return revolutionaryQuote
      }

      console.log(`⚠️ Revolutionary Service failed for ${symbol}, trying fallback methods...`)

      // Priority 2: Fallback to external APIs based on symbol type
      if (symbol.includes('/')) {
        // Forex pair
        return this.getForexQuote(symbol)
      } else if (symbol.includes('USD') && (symbol.startsWith('BTC') || symbol.startsWith('ETH') || symbol.startsWith('XRP'))) {
        // Crypto
        return this.getCryptoQuote(symbol)
      } else if (symbol === 'GOLD' || symbol === 'SILVER' || symbol === 'OIL') {
        // Commodities - use special symbols
        const commodityMap: { [key: string]: string } = {
          'GOLD': 'GC=F',
          'SILVER': 'SI=F',
          'OIL': 'CL=F'
        }
        return this.getFinnhubQuote(commodityMap[symbol] || symbol)
      } else {
        // Default to stock/index
        return this.getStockQuote(symbol)
      }
    } catch (error) {
      console.error(`Error getting quote for ${symbol}:`, error)
      return null
    }
  }

  // Get multiple quotes efficiently
  async getMultipleQuotes(symbols: string[]): Promise<{ [symbol: string]: MarketQuote | null }> {
    const results: { [symbol: string]: MarketQuote | null } = {}
    
    // Process in batches to avoid rate limits
    const batchSize = 5
    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize)
      const promises = batch.map(symbol => this.getQuote(symbol))
      const batchResults = await Promise.allSettled(promises)
      
      batch.forEach((symbol, index) => {
        const result = batchResults[index]
        results[symbol] = result.status === 'fulfilled' ? result.value : null
      })
      
      // Add small delay between batches to respect rate limits
      if (i + batchSize < symbols.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    return results
  }

  // Get historical data from Revolutionary WebSocket Service (Primary method)
  async getRevolutionaryHistoricalData(symbol: string, timeframe: string, limit: number = 100): Promise<CandleData[]> {
    try {
      const revolutionaryUrl = `${process.env.REVOLUTIONARY_SERVICE_URL || 'http://localhost:3002'}/api/historical/${symbol}/${timeframe}?limit=${limit}`
      
      console.log(`📊 Fetching historical data from Revolutionary Service: ${symbol}/${timeframe}`)
      
      const response = await axios.get(revolutionaryUrl, { 
        timeout: 35000, // Long timeout for historical data
        headers: {
          'User-Agent': 'PropFirm-Backend/1.0.0'
        }
      })
      
      if (response.data && response.data.success && response.data.data) {
        const ohlcvData = response.data.data
        
        const candles: CandleData[] = ohlcvData.map((item: any) => ({
          symbol,
          timeframe,
          open: item.open || item.close || 0,
          high: item.high || item.close || 0,
          low: item.low || item.close || 0,
          close: item.close || 0,
          volume: item.volume || 0,
          timestamp: new Date(item.time)
        }))

        console.log(`✅ Revolutionary Service historical data for ${symbol}/${timeframe}: ${candles.length} candles`)
        return candles

      } else {
        console.warn(`⚠️ Revolutionary Service returned invalid historical data for ${symbol}/${timeframe}`)
        return []
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          console.error(`⏰ Revolutionary historical data timeout for ${symbol}/${timeframe} - Chart session may be slow`)
        } else if (error.code === 'ECONNREFUSED') {
          console.error(`🔌 Revolutionary Service connection refused for ${symbol}/${timeframe} - service not running?`)
        } else if (error.response?.status === 500) {
          console.error(`💥 Revolutionary Service historical error for ${symbol}/${timeframe}:`, error.response.data?.details || error.response.data?.error)
        } else {
          console.error(`❌ Revolutionary Service historical error for ${symbol}/${timeframe}:`, error.message)
        }
      } else {
        console.error(`Revolutionary Service historical error for ${symbol}/${timeframe}:`, error)
      }
      return []
    }
  }

  // Get historical data from Enterprise API (Fallback method)
  async getEnterpriseHistoricalData(symbol: string, timeframe: string, limit: number = 100): Promise<CandleData[]> {
    try {
      const response = await enterpriseMarketDataClient.getHistoricalData(symbol, timeframe, { limit })
      
      const candles: CandleData[] = response.data.map(item => ({
        symbol,
        timeframe,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume,
        timestamp: new Date(item.time * 1000)
      }))

      console.log(`📈 Fetched ${candles.length} historical candles for ${symbol}/${timeframe} from Enterprise API`)
      return candles

    } catch (error) {
      console.error(`Enterprise API historical data error for ${symbol}/${timeframe}:`, error)
      return []
    }
  }

  // PHASE 1: Enhanced historical candle data with unlimited range support (Updated)
  async getCandleData(symbol: string, timeframe: string, limit: number = 10000): Promise<CandleData[]> {
    const maxRetries = 3
    let lastError: Error | null = null
    
    // Check if we should use live data
    const useLiveData = process.env.NEXT_PUBLIC_USE_LIVE_DATA === 'true'
    const debugMode = process.env.NEXT_PUBLIC_DEBUG_MODE === 'true'

    if (debugMode) {
      console.log(`📊 [MarketDataService] getCandleData - Symbol: ${symbol}, Timeframe: ${timeframe}, UseLiveData: ${useLiveData}`)
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Priority 1: Try Revolutionary WebSocket Service first (Zero 429 errors)
        const revolutionaryData = await this.getRevolutionaryHistoricalData(symbol, timeframe, Math.min(limit, 1000))
        if (revolutionaryData.length > 0) {
          console.log(`✅ [MarketDataService] Using Revolutionary Service data for ${symbol}/${timeframe}`)
          return revolutionaryData
        }

        // Priority 2: Try Enterprise API as fallback
        const enterpriseData = await this.getEnterpriseHistoricalData(symbol, timeframe, Math.min(limit, 1000))
        if (enterpriseData.length > 0) {
          console.log(`✅ [MarketDataService] Using Enterprise API data for ${symbol}/${timeframe}`)
          return enterpriseData
        }

        // Use mock data if live data is disabled, TradingView has failed too many times, or forced mock mode
        const forceMock = process.env.NEXT_PUBLIC_TRADINGVIEW_FORCE_MOCK === 'true'
        if (!useLiveData || this.tradingViewFailureCount >= this.maxTradingViewFailures || forceMock) {
          if (debugMode) {
            const reason = !useLiveData ? 'live data disabled' 
              : forceMock ? 'forced mock mode'
              : 'TradingView failures exceeded'
            console.log(`📊 [MarketDataService] Using mock data for ${symbol} (${reason})`)
          }
          return this.generateMockCandleData(symbol, timeframe, limit)
        }

        // Try TradingView WebSocket client first with timeout
        try {
          if (debugMode) {
            console.log(`📊 [MarketDataService] Attempting TradingView WebSocket connection for ${symbol} (attempt ${attempt}/${maxRetries})`)
          }
          
          const tradingViewClient = getTradingViewWebSocketClient()
          
          // Ensure connection with timeout
          if (!tradingViewClient.isConnected()) {
            if (debugMode) {
              console.log(`📊 [MarketDataService] TradingView WebSocket not connected, attempting connection...`)
            }
            const connectPromise = tradingViewClient.connect()
            const timeoutPromise = new Promise<never>((_, reject) => 
              setTimeout(() => reject(new Error('TradingView WebSocket connection timeout')), 5000)
            )
            await Promise.race([connectPromise, timeoutPromise])
            
            if (debugMode) {
              console.log(`📊 [MarketDataService] TradingView WebSocket connection successful`)
            }
          }

          // Get candlestick data with timeout
          const dataPromise = tradingViewClient.getCandlestickData(symbol, timeframe, limit)
          const timeoutPromise = new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('TradingView WebSocket data fetch timeout')), 8000)
          )
          
          const tvCandles = await Promise.race([dataPromise, timeoutPromise])
          
          // Validate data quality
          if (!tvCandles || tvCandles.length === 0) {
            throw new Error('No data received from TradingView')
          }

          if (debugMode) {
            console.log(`📊 [MarketDataService] Raw TradingView candle sample:`, tvCandles.slice(0, 2))
          }

          // Transform TradingView data to our format with better fallback handling
          const candles = tvCandles.map((candle: TradingViewCandle) => {
            // Use close price as fallback for missing OHLC data
            const close = candle.close || 0
            const open = candle.open || close
            const high = candle.high || Math.max(open, close) || close
            const low = candle.low || Math.min(open, close) || close
            
            return {
              symbol,
              timeframe,
              open: open,
              high: high,
              low: low,
              close: close,
              volume: candle.volume || 0,
              timestamp: new Date(candle.time * 1000)
            }
          })

          // More lenient validation - just need valid close price and timestamp
          const validCandles = candles.filter(candle => 
            candle.close > 0 && candle.timestamp && !isNaN(candle.timestamp.getTime())
          )

          if (debugMode) {
            console.log(`📊 [MarketDataService] Filtered ${validCandles.length}/${candles.length} valid candles`)
            console.log(`📊 [MarketDataService] Sample valid candle:`, validCandles[0])
          }

          if (validCandles.length === 0) {
            throw new Error('No valid candles after transformation')
          }

          // Reset failure count on success
          this.tradingViewFailureCount = 0
          
          if (debugMode) {
            console.log(`✅ [MarketDataService] Successfully fetched ${validCandles.length} candles from TradingView WebSocket for ${symbol}`)
          }
          return validCandles

        } catch (tvError) {
          lastError = tvError instanceof Error ? tvError : new Error(String(tvError))
          this.tradingViewFailureCount++
          if (debugMode) {
            console.warn(`⚠️ [MarketDataService] TradingView WebSocket attempt ${attempt} failed for ${symbol} (failures: ${this.tradingViewFailureCount}):`, tvError)
          }
          
          if (attempt < maxRetries) {
            // Wait before retry with exponential backoff
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
            console.log(`Retrying in ${delay}ms...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
          
          // Try alternative data sources before falling back to mock data
          try {
            const quote = await this.getQuote(symbol)
            if (quote) {
              console.log(`Using current quote to generate basic candle data for ${symbol}`)
              return this.generateCandleDataFromQuote(quote, timeframe, limit)
            }
          } catch (quoteError) {
            console.warn(`Quote fallback also failed for ${symbol}:`, quoteError)
          }
          
          // Final fallback to mock data
          if (debugMode) {
            console.log(`🔄 [MarketDataService] All data sources failed, falling back to mock data for ${symbol}`)
          }
          return this.generateMockCandleData(symbol, timeframe, limit)
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.error(`Attempt ${attempt} failed for ${symbol}:`, error)
        
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // All attempts failed, return mock data with error logging
    console.error(`All ${maxRetries} attempts failed for ${symbol}. Last error:`, lastError)
    return this.generateMockCandleData(symbol, timeframe, limit)
  }

  // Generate candle data from a single quote (fallback method)
  private generateCandleDataFromQuote(quote: MarketQuote, timeframe: string, limit: number): CandleData[] {
    const candles: CandleData[] = []
    const now = Date.now()
    const timeframeMs = this.getTimeframeMs(timeframe)
    
    // Generate historical data based on current quote
    for (let i = limit - 1; i >= 0; i--) {
      const timestamp = new Date(now - (i * timeframeMs))
      const variation = (Math.random() - 0.5) * quote.last * 0.01 // 1% max variation
      
      const open = quote.last + variation
      const close = quote.last + (Math.random() - 0.5) * quote.last * 0.005 // 0.5% variation
      const high = Math.max(open, close) + Math.random() * quote.last * 0.002
      const low = Math.min(open, close) - Math.random() * quote.last * 0.002
      
      candles.push({
        symbol: quote.symbol,
        timeframe,
        open: Number(open.toFixed(5)),
        high: Number(high.toFixed(5)),
        low: Number(low.toFixed(5)),
        close: Number(close.toFixed(5)),
        volume: quote.volume || Math.floor(Math.random() * 1000000),
        timestamp
      })
    }
    
    return candles.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
  }

  // Generate mock candle data for testing/development
  private generateMockCandleData(symbol: string, timeframe: string, limit: number): CandleData[] {
    const candles: CandleData[] = []
    const basePrice = this.getMockBasePrice(symbol)
    const now = Date.now()
    
    // Timeframe to milliseconds
    const timeframeMs = this.getTimeframeMs(timeframe)
    
    let previousClose = basePrice
    
    for (let i = limit - 1; i >= 0; i--) {
      const timestamp = new Date(now - (i * timeframeMs))
      
      // Generate realistic OHLC
      const volatility = basePrice * 0.001 * Math.random() // 0.1% max volatility
      const open = i === limit - 1 ? basePrice : previousClose + (Math.random() - 0.5) * volatility * 0.2
      
      const trend = (Math.random() - 0.5) * volatility
      const close = open + trend
      
      const highExtra = Math.random() * volatility * 0.5
      const lowExtra = Math.random() * volatility * 0.5
      
      const high = Math.max(open, close) + highExtra
      const low = Math.min(open, close) - lowExtra
      
      const volume = Math.floor(Math.random() * 1000000) + 100000
      
      candles.push({
        symbol,
        timeframe,
        open: Number(open.toFixed(5)),
        high: Number(high.toFixed(5)),
        low: Number(low.toFixed(5)),
        close: Number(close.toFixed(5)),
        volume,
        timestamp
      })
      
      previousClose = close
    }
    
    return candles.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
  }

  // Convert timeframe to milliseconds
  private getTimeframeMs(timeframe: string): number {
    const timeframeMap: { [key: string]: number } = {
      '1M': 60 * 1000,
      '5M': 5 * 60 * 1000,
      '15M': 15 * 60 * 1000,
      '1H': 60 * 60 * 1000,
      '4H': 4 * 60 * 60 * 1000,
      '1D': 24 * 60 * 60 * 1000
    }
    return timeframeMap[timeframe] || 60 * 60 * 1000
  }

  // Subscribe to real-time candle updates with enhanced error handling
  subscribeToRealTimeCandles(
    symbol: string, 
    timeframe: string, 
    callback: (candle: CandleData) => void
  ): () => void {
    let unsubscribeFunction: (() => void) | null = null
    let reconnectAttempt = 0
    const maxReconnectAttempts = 5

    const attemptSubscription = async () => {
      try {
        const tradingViewClient = getTradingViewWebSocketClient()
        
        // Ensure connection with timeout
        if (!tradingViewClient.isConnected()) {
          console.log(`📊 Connecting TradingView WebSocket client for ${symbol}...`)
          const connectPromise = tradingViewClient.connect()
          const timeoutPromise = new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('TradingView WebSocket connection timeout')), 10000)
          )
          await Promise.race([connectPromise, timeoutPromise])
        }

        const unsubscribeCallback = (tvCandle: TradingViewCandle) => {
          try {
            // Validate candle data
            if (!tvCandle || typeof tvCandle.time !== 'number' || tvCandle.time <= 0) {
              console.warn(`Invalid candle data received for ${symbol}:`, tvCandle)
              return
            }

            const candle: CandleData = {
              symbol,
              timeframe,
              open: tvCandle.open || 0,
              high: tvCandle.high || 0,
              low: tvCandle.low || 0,
              close: tvCandle.close || 0,
              volume: tvCandle.volume || 0,
              timestamp: new Date(tvCandle.time * 1000)
            }

            // Additional validation
            if (candle.open <= 0 || candle.high <= 0 || candle.low <= 0 || candle.close <= 0) {
              console.warn(`Invalid price data in candle for ${symbol}:`, candle)
              return
            }

            callback(candle)
          } catch (callbackError) {
            console.error(`Error in candle callback for ${symbol}:`, callbackError)
          }
        }

        tradingViewClient.onSymbolData(symbol, unsubscribeCallback)
        
        // Set up error monitoring
        tradingViewClient.onError((error: Error) => {
          console.error(`TradingView error for ${symbol}:`, error)
          
          // Attempt reconnection if not at max attempts
          if (reconnectAttempt < maxReconnectAttempts) {
            reconnectAttempt++
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempt - 1), 10000)
            console.log(`📊 Attempting to reconnect TradingView for ${symbol} in ${delay}ms (attempt ${reconnectAttempt}/${maxReconnectAttempts})`)
            
            setTimeout(() => {
              attemptSubscription().catch(console.error)
            }, delay)
          } else {
            console.error(`📊 Max reconnection attempts reached for ${symbol}`)
          }
        })
        
        unsubscribeFunction = () => {
          try {
            tradingViewClient.offSymbolData(symbol, unsubscribeCallback)
          } catch (error) {
            console.warn(`Error unsubscribing from ${symbol}:`, error)
          }
        }

        console.log(`📊 Successfully subscribed to real-time candles for ${symbol}`)
        reconnectAttempt = 0 // Reset on successful connection

      } catch (error) {
        console.error(`Failed to subscribe to real-time candles for ${symbol}:`, error)
        
        if (reconnectAttempt < maxReconnectAttempts) {
          reconnectAttempt++
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempt - 1), 10000)
          console.log(`📊 Retrying subscription for ${symbol} in ${delay}ms (attempt ${reconnectAttempt}/${maxReconnectAttempts})`)
          
          setTimeout(() => {
            attemptSubscription().catch(console.error)
          }, delay)
        }
      }
    }

    // Start initial subscription attempt
    attemptSubscription().catch(console.error)
    
    // Return unsubscribe function
    return () => {
      if (unsubscribeFunction) {
        unsubscribeFunction()
        unsubscribeFunction = null
      }
    }
  }

  // Clean up old cache entries
  cleanCache(): void {
    for (const [key, value] of this.cache.entries()) {
      if (!this.isCacheValid(value.timestamp)) {
        this.cache.delete(key)
      }
    }
  }
}

// Export singleton instance
export const marketDataService = new MarketDataService()
export default marketDataService