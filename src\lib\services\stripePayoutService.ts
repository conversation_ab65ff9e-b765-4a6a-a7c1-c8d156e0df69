/**
 * Stripe Payout Service
 * Handles real money transfers to traders via Stripe
 */

import Strip<PERSON> from 'stripe'
import { supabase } from '@/lib/supabase/client'
import type { Payout } from '@/types/database'

// Initialize Stripe with secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia'
})

interface PayoutBankDetails {
  accountHolderName: string
  accountNumber: string
  routingNumber: string
  bankName?: string
  accountType: 'checking' | 'savings'
}

interface PayoutResult {
  success: boolean
  message: string
  stripeTransferId?: string
  stripePayoutId?: string
  error?: string
}

interface ConnectedAccountInfo {
  stripeAccountId: string
  accountStatus: 'active' | 'pending' | 'restricted' | 'rejected'
  requiresVerification: boolean
  missingFields: string[]
}

export class StripePayoutService {
  private readonly MINIMUM_PAYOUT = 1.00 // $1.00 minimum payout in USD
  private readonly MAXIMUM_PAYOUT = 100000.00 // $100,000 maximum payout

  /**
   * Create or update Stripe Connect account for a user
   */
  async createOrUpdateConnectedAccount(
    userId: string, 
    userInfo: {
      email: string
      firstName: string
      lastName: string
      country: string
      businessType?: 'individual' | 'company'
    }
  ): Promise<{ success: boolean; accountId?: string; onboardingUrl?: string; message: string }> {
    try {
      // Check if user already has a connected account
      const { data: existingUser } = await supabase
        .from('users')
        .select('stripe_account_id')
        .eq('id', userId)
        .single()

      let stripeAccountId = existingUser?.stripe_account_id

      if (!stripeAccountId) {
        // Create new Stripe Connect account
        const account = await stripe.accounts.create({
          type: 'express',
          country: userInfo.country,
          email: userInfo.email,
          business_type: userInfo.businessType || 'individual',
          individual: {
            first_name: userInfo.firstName,
            last_name: userInfo.lastName,
            email: userInfo.email
          },
          capabilities: {
            transfers: { requested: true }
          },
          settings: {
            payouts: {
              schedule: {
                interval: 'manual' // We'll handle payouts manually
              }
            }
          }
        })

        stripeAccountId = account.id

        // Save account ID to user record
        await supabase
          .from('users')
          .update({ stripe_account_id: stripeAccountId })
          .eq('id', userId)
      }

      // Create account onboarding link
      const accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?setup=refresh`,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?setup=complete`,
        type: 'account_onboarding'
      })

      return {
        success: true,
        accountId: stripeAccountId,
        onboardingUrl: accountLink.url,
        message: 'Connected account created successfully'
      }
    } catch (error) {
      console.error('Error creating connected account:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create connected account'
      }
    }
  }

  /**
   * Get connected account status and requirements
   */
  async getConnectedAccountInfo(userId: string): Promise<ConnectedAccountInfo | null> {
    try {
      const { data: user } = await supabase
        .from('users')
        .select('stripe_account_id')
        .eq('id', userId)
        .single()

      if (!user?.stripe_account_id) {
        return null
      }

      const account = await stripe.accounts.retrieve(user.stripe_account_id)

      return {
        stripeAccountId: account.id,
        accountStatus: account.charges_enabled ? 'active' : 
                     account.details_submitted ? 'pending' : 'restricted',
        requiresVerification: !account.details_submitted || 
                            (account.requirements?.currently_due?.length || 0) > 0,
        missingFields: account.requirements?.currently_due || []
      }
    } catch (error) {
      console.error('Error getting connected account info:', error)
      return null
    }
  }

  /**
   * Process payout via Stripe transfer to connected account
   */
  async processStripePayout(payoutId: string): Promise<PayoutResult> {
    try {
      // Get payout details
      const { data: payout, error: payoutError } = await supabase
        .from('payouts')
        .select(`
          *,
          users!inner(stripe_account_id)
        `)
        .eq('id', payoutId)
        .single()

      if (payoutError || !payout) {
        return { success: false, message: 'Payout not found' }
      }

      const user = payout.users as any
      if (!user.stripe_account_id) {
        return { success: false, message: 'User does not have a connected Stripe account' }
      }

      // Validate payout amount
      if (payout.amount < this.MINIMUM_PAYOUT) {
        return { 
          success: false, 
          message: `Payout amount below minimum ($${this.MINIMUM_PAYOUT})` 
        }
      }

      if (payout.amount > this.MAXIMUM_PAYOUT) {
        return { 
          success: false, 
          message: `Payout amount exceeds maximum ($${this.MAXIMUM_PAYOUT})` 
        }
      }

      // Check connected account status
      const account = await stripe.accounts.retrieve(user.stripe_account_id)
      
      if (!account.charges_enabled || !account.payouts_enabled) {
        return { 
          success: false, 
          message: 'Connected account is not enabled for payouts' 
        }
      }

      // Create transfer to connected account
      const transfer = await stripe.transfers.create({
        amount: Math.round(payout.amount * 100), // Convert to cents
        currency: 'usd',
        destination: user.stripe_account_id,
        description: `Payout for trading profits - ${payout.description}`,
        metadata: {
          payoutId: payout.id,
          userId: payout.user_id,
          accountId: payout.account_id
        }
      })

      // Update payout record with Stripe transfer ID
      const { error: updateError } = await supabase
        .from('payouts')
        .update({
          status: 'processing',
          stripe_transfer_id: transfer.id,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', payoutId)

      if (updateError) {
        console.error('Error updating payout record:', updateError)
        // Don't fail the payout, just log the error
      }

      // Log the payout event
      await this.logPayoutEvent(payoutId, 'stripe_transfer_created', {
        transferId: transfer.id,
        amount: payout.amount,
        stripeAccountId: user.stripe_account_id
      })

      return {
        success: true,
        message: 'Payout processed successfully',
        stripeTransferId: transfer.id
      }
    } catch (error) {
      console.error('Error processing Stripe payout:', error)
      
      // Update payout status to failed
      await supabase
        .from('payouts')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', payoutId)

      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to process payout',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Handle Stripe webhook events for payout status updates
   */
  async handleStripeWebhook(event: Stripe.Event): Promise<void> {
    try {
      switch (event.type) {
        case 'transfer.created':
          await this.handleTransferCreated(event.data.object as Stripe.Transfer)
          break
          
        case 'transfer.updated':
          await this.handleTransferUpdated(event.data.object as Stripe.Transfer)
          break
          
        case 'transfer.failed':
          await this.handleTransferFailed(event.data.object as Stripe.Transfer)
          break
          
        case 'account.updated':
          await this.handleAccountUpdated(event.data.object as Stripe.Account)
          break
          
        default:
          console.log(`Unhandled Stripe webhook event: ${event.type}`)
      }
    } catch (error) {
      console.error('Error handling Stripe webhook:', error)
      throw error
    }
  }

  /**
   * Handle transfer created webhook
   */
  private async handleTransferCreated(transfer: Stripe.Transfer): Promise<void> {
    const payoutId = transfer.metadata?.payoutId
    if (!payoutId) return

    await supabase
      .from('payouts')
      .update({
        status: 'processing',
        stripe_transfer_id: transfer.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', payoutId)

    await this.logPayoutEvent(payoutId, 'transfer_created', { transferId: transfer.id })
  }

  /**
   * Handle transfer updated webhook
   */
  private async handleTransferUpdated(transfer: Stripe.Transfer): Promise<void> {
    const payoutId = transfer.metadata?.payoutId
    if (!payoutId) return

    let status: string
    switch (transfer.status) {
      case 'paid':
        status = 'succeeded'
        break
      case 'pending':
        status = 'processing'
        break
      case 'failed':
        status = 'failed'
        break
      default:
        status = 'processing'
    }

    await supabase
      .from('payouts')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', payoutId)

    await this.logPayoutEvent(payoutId, 'transfer_updated', { 
      transferId: transfer.id, 
      status: transfer.status 
    })
  }

  /**
   * Handle transfer failed webhook
   */
  private async handleTransferFailed(transfer: Stripe.Transfer): Promise<void> {
    const payoutId = transfer.metadata?.payoutId
    if (!payoutId) return

    await supabase
      .from('payouts')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString()
      })
      .eq('id', payoutId)

    await this.logPayoutEvent(payoutId, 'transfer_failed', { 
      transferId: transfer.id,
      failureCode: transfer.failure_code,
      failureMessage: transfer.failure_message
    })
  }

  /**
   * Handle account updated webhook
   */
  private async handleAccountUpdated(account: Stripe.Account): Promise<void> {
    // Update user's account status if needed
    await supabase
      .from('users')
      .update({
        stripe_account_status: account.charges_enabled ? 'active' : 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_account_id', account.id)
  }

  /**
   * Get payout fees and estimates
   */
  async getPayoutEstimate(amount: number): Promise<{
    payoutAmount: number
    stripeFee: number
    netAmount: number
    estimatedArrival: string
  }> {
    // Stripe transfer fees (as of 2024, subject to change)
    const stripeFee = 0.25 // $0.25 per transfer
    const netAmount = Math.max(0, amount - stripeFee)

    // Estimate arrival time (typically 1-2 business days for US banks)
    const estimatedArrival = new Date()
    estimatedArrival.setDate(estimatedArrival.getDate() + 2)

    return {
      payoutAmount: amount,
      stripeFee,
      netAmount,
      estimatedArrival: estimatedArrival.toLocaleDateString()
    }
  }

  /**
   * Batch process multiple payouts
   */
  async batchProcessPayouts(payoutIds: string[]): Promise<{
    processed: number
    failed: number
    results: Array<{ payoutId: string; result: PayoutResult }>
  }> {
    const results = []
    let processed = 0
    let failed = 0

    for (const payoutId of payoutIds) {
      try {
        const result = await this.processStripePayout(payoutId)
        results.push({ payoutId, result })
        
        if (result.success) {
          processed++
        } else {
          failed++
        }
      } catch (error) {
        console.error(`Error processing payout ${payoutId}:`, error)
        results.push({
          payoutId,
          result: {
            success: false,
            message: 'Processing error occurred'
          }
        })
        failed++
      }
    }

    return { processed, failed, results }
  }

  /**
   * Log payout events for audit trail
   */
  private async logPayoutEvent(payoutId: string, event: string, data: any): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          resource_type: 'payout',
          resource_id: payoutId,
          action: event,
          new_values: data
        })
    } catch (error) {
      console.error('Failed to log payout event:', error)
    }
  }
}

// Export singleton instance
export const stripePayoutService = new StripePayoutService()