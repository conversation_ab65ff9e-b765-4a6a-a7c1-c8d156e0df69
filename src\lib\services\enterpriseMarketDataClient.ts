/**
 * Enterprise Market Data API Client
 * Connects to the FastAPI server (port 8001) with 140+ symbols across 6 asset classes
 * Features: Zero 429 errors, sub-30s data freshness, WebSocket streaming
 */

import { io, Socket } from 'socket.io-client'

// Types based on API documentation
export interface EnterpriseQuote {
  symbol: string
  price: number
  bid: number
  ask: number
  change: number
  change_percent: number
  volume: number
  timestamp: string
  source?: string
}

export interface AllQuotesResponse {
  total_symbols: number
  last_updated: string
  collection_time_ms: number
  quotes: Record<string, EnterpriseQuote>
  metadata: {
    forex_pairs: number
    cryptocurrencies: number
    stocks: number
    commodities: number
    indices: number
    etfs: number
    rate_limit_status: 'HEALTHY' | 'WARNING'
    circuit_breaker_state: 'CLOSED' | 'OPEN' | 'HALF_OPEN'
    tokens_available: number
    last_429_error: string | null
  }
}

export interface SingleQuoteResponse {
  symbol: string
  data: EnterpriseQuote
  metadata: {
    source: string
    timestamp: string
    age_seconds: number
    cached: boolean
    asset_class: string
  }
}

export interface HistoricalData {
  time: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface HistoricalResponse {
  symbol: string
  timeframe: string
  requested_limit: number
  actual_count: number
  data: HistoricalData[]
  metadata: {
    source: string
    cached: boolean
    cache_timestamp: string
    processing_time_ms: number
  }
}

export interface SearchResult {
  symbol: string
  name: string
  description: string
  asset_class: string
  category: string
  supported_timeframes: string[]
  real_time_available: boolean
  last_price: number
  change_24h: number
  volume_24h: number
}

export interface SearchResponse {
  query: string
  asset_class_filter: string | null
  total_matches: number
  results: SearchResult[]
  suggestions: string[]
}

export interface SystemHealth {
  status: string
  services: {
    fastapi: string
    nodejs: string
    redis: string
    collector: string
  }
  data_collection: {
    total_symbols: number
    success_rate: number
    avg_response_time_ms: number
    last_collection_time: string
    "429_errors_last_hour": number
    circuit_breaker_state: string
  }
  performance: {
    uptime_seconds: number
    requests_per_minute: number
    cache_hit_rate: number
    memory_usage_mb: number
  }
}

export class EnterpriseMarketDataClient {
  private baseUrl: string
  private apiKey: string
  private headers: Record<string, string>
  private wsConnections: Map<string, Socket> = new Map()

  constructor(apiKey?: string, baseUrl?: string) {
    this.baseUrl = baseUrl || process.env.NEXT_PUBLIC_MARKET_DATA_API_URL || 'http://localhost:8001'
    this.apiKey = apiKey || process.env.NEXT_PUBLIC_MARKET_DATA_API_KEY || process.env.MARKET_DATA_API_KEY || ''
    this.headers = {
      'X-API-Key': this.apiKey,
      'Content-Type': 'application/json',
    }

    if (!this.apiKey && process.env.NEXT_PUBLIC_USE_ENTERPRISE_API === 'true') {
      console.warn('⚠️ Enterprise Market Data API key not configured - data may be limited')
    }
  }

  /**
   * Get all 140+ real-time market quotes (Primary Endpoint)
   */
  async getAllQuotes(): Promise<AllQuotesResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/marketdata/quotes/all`, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Enterprise API - Error fetching all quotes:', error)
      throw error
    }
  }

  /**
   * Get single symbol quote
   */
  async getQuote(symbol: string): Promise<SingleQuoteResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/marketdata/quote/${encodeURIComponent(symbol)}`, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Enterprise API - Error fetching quote for ${symbol}:`, error)
      throw error
    }
  }

  /**
   * Get historical OHLCV data
   */
  async getHistoricalData(
    symbol: string,
    timeframe: string,
    options?: {
      limit?: number
      start_time?: number
      end_time?: number
    }
  ): Promise<HistoricalResponse> {
    try {
      const params = new URLSearchParams()
      if (options?.limit) params.append('limit', options.limit.toString())
      if (options?.start_time) params.append('start_time', options.start_time.toString())
      if (options?.end_time) params.append('end_time', options.end_time.toString())

      const url = `${this.baseUrl}/api/marketdata/historical/${encodeURIComponent(symbol)}/${encodeURIComponent(timeframe)}`
      const fullUrl = params.toString() ? `${url}?${params.toString()}` : url

      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Enterprise API - Error fetching historical data for ${symbol}/${timeframe}:`, error)
      throw error
    }
  }

  /**
   * Search for trading symbols
   */
  async searchSymbols(
    query: string,
    options?: {
      asset_class?: string
      limit?: number
    }
  ): Promise<SearchResponse> {
    try {
      const params = new URLSearchParams({ query })
      if (options?.asset_class) params.append('asset_class', options.asset_class)
      if (options?.limit) params.append('limit', options.limit.toString())

      const response = await fetch(`${this.baseUrl}/api/marketdata/search?${params.toString()}`, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`Enterprise API - Error searching symbols for "${query}":`, error)
      throw error
    }
  }

  /**
   * Get system health status
   */
  async getHealth(): Promise<SystemHealth> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Enterprise API - Error fetching system health:', error)
      throw error
    }
  }

  /**
   * Get complete symbol list
   */
  async getAllSymbols(): Promise<{
    total_symbols: number
    asset_classes: Record<string, {
      count: number
      symbols: string[]
    }>
    last_updated: string
    data_sources: string[]
    collection_frequency: string
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/admin/symbols`, {
        method: 'GET',
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Enterprise API - Error fetching symbol list:', error)
      throw error
    }
  }

  /**
   * Connect to single symbol WebSocket stream
   */
  connectToRealTimeData(
    symbol: string,
    userId?: string,
    onMessage?: (data: any) => void,
    onError?: (error: Error) => void,
    onConnect?: () => void,
    onDisconnect?: (reason: string) => void
  ): Socket {
    const connectionId = `single_${symbol}`
    
    // Close existing connection if any
    this.disconnectWebSocket(connectionId)

    const params = new URLSearchParams({
      api_key: this.apiKey,
      user_id: userId || `trader_${Date.now()}`
    })

    const wsUrl = `ws://localhost:8001/api/marketdata/realtime/${encodeURIComponent(symbol)}?${params.toString()}`
    
    try {
      // Note: This is a WebSocket connection, not Socket.IO
      // We'll need to use native WebSocket for the enterprise API
      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log(`🔗 Connected to ${symbol} real-time stream`)
        if (onConnect) onConnect()
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (onMessage) onMessage(data)
        } catch (error) {
          console.error(`Error parsing WebSocket message for ${symbol}:`, error)
        }
      }

      ws.onclose = (event) => {
        console.log(`🔌 ${symbol} stream closed: ${event.reason}`)
        this.wsConnections.delete(connectionId)
        if (onDisconnect) onDisconnect(event.reason)
      }

      ws.onerror = (event) => {
        console.error(`❌ WebSocket error for ${symbol}:`, event)
        if (onError) onError(new Error(`WebSocket error for ${symbol}`))
      }

      // Store connection (casting WebSocket to Socket for interface compatibility)
      this.wsConnections.set(connectionId, ws as any)

      return ws as any
    } catch (error) {
      console.error(`Failed to create WebSocket connection for ${symbol}:`, error)
      if (onError) onError(error instanceof Error ? error : new Error(String(error)))
      throw error
    }
  }

  /**
   * Connect to multi-symbol batch WebSocket stream
   */
  connectToBatchStream(
    symbols: string[],
    userId?: string,
    onMessage?: (data: any) => void,
    onError?: (error: Error) => void,
    onConnect?: () => void,
    onDisconnect?: (reason: string) => void
  ): Socket {
    const connectionId = 'batch_stream'
    
    // Close existing connection if any
    this.disconnectWebSocket(connectionId)

    const params = new URLSearchParams({
      api_key: this.apiKey,
      symbols: symbols.join(','),
      user_id: userId || `dashboard_${Date.now()}`
    })

    const wsUrl = `ws://localhost:8001/api/marketdata/realtime/batch?${params.toString()}`
    
    try {
      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log(`🚀 Connected to batch stream for ${symbols.length} symbols`)
        if (onConnect) onConnect()
      }

      ws.onmessage = (event) => {
        try {
          const update = JSON.parse(event.data)
          if (onMessage) onMessage(update)
        } catch (error) {
          console.error('Error parsing batch WebSocket message:', error)
        }
      }

      ws.onclose = (event) => {
        console.log(`🔌 Batch stream closed: ${event.reason}`)
        this.wsConnections.delete(connectionId)
        if (onDisconnect) onDisconnect(event.reason)
      }

      ws.onerror = (event) => {
        console.error('❌ Batch WebSocket error:', event)
        if (onError) onError(new Error('Batch WebSocket error'))
      }

      // Store connection
      this.wsConnections.set(connectionId, ws as any)

      return ws as any
    } catch (error) {
      console.error('Failed to create batch WebSocket connection:', error)
      if (onError) onError(error instanceof Error ? error : new Error(String(error)))
      throw error
    }
  }

  /**
   * Disconnect WebSocket connection
   */
  disconnectWebSocket(connectionId: string): void {
    const connection = this.wsConnections.get(connectionId)
    if (connection) {
      try {
        ;(connection as any).close()
      } catch (error) {
        console.warn(`Error closing WebSocket connection ${connectionId}:`, error)
      }
      this.wsConnections.delete(connectionId)
    }
  }

  /**
   * Disconnect all WebSocket connections
   */
  disconnectAll(): void {
    for (const [connectionId] of this.wsConnections) {
      this.disconnectWebSocket(connectionId)
    }
  }

  /**
   * Get connection status
   */
  isConnected(connectionId?: string): boolean {
    if (connectionId) {
      const connection = this.wsConnections.get(connectionId)
      return connection ? (connection as any).readyState === WebSocket.OPEN : false
    }
    
    // Check if any connection is active
    for (const connection of this.wsConnections.values()) {
      if ((connection as any).readyState === WebSocket.OPEN) {
        return true
      }
    }
    return false
  }

  /**
   * Get active connection count
   */
  getActiveConnectionCount(): number {
    let count = 0
    for (const connection of this.wsConnections.values()) {
      if ((connection as any).readyState === WebSocket.OPEN) {
        count++
      }
    }
    return count
  }
}

// Create singleton instance
export const enterpriseMarketDataClient = new EnterpriseMarketDataClient()

export default enterpriseMarketDataClient