'use client'

/**
 * 🚀 Revolutionary Market Data Hook
 * 
 * Direct connection to Revolutionary WebSocket Service for fastest possible market data.
 * This hook provides real-time market data with zero 429 errors and sub-second updates.
 * Now uses shared connection manager to prevent duplicate connections.
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import RevolutionaryConnectionManager from '@/lib/services/revolutionaryConnectionManager'

interface MarketData {
  symbol: string
  data: {
    lp?: number  // Last price
    bid?: number
    ask?: number
    ch?: number  // Change
    chp?: number // Change percent
    volume?: number
  }
  timestamp: string
  source: string
  connectionId: string
}

interface RevolutionaryMarketDataHook {
  marketData: Record<string, MarketData>
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  isConnected: boolean
  subscribe: (symbol: string) => void
  unsubscribe: (symbol: string) => void
  connect: () => void
  disconnect: () => void
  subscriptions: Set<string>
  lastError: string | null
  connectionStats: {
    connectedAt: Date | null
    reconnectCount: number
    messageCount: number
  }
}

interface UseRevolutionaryMarketDataOptions {
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectDelay?: number
  symbols?: string[]
  onError?: (error: string) => void
  onConnectionChange?: (status: string) => void
  onMarketData?: (data: MarketData) => void
}

const REVOLUTIONARY_SERVICE_URL = process.env.NEXT_PUBLIC_REVOLUTIONARY_SERVICE_URL || 'http://localhost:3002'

export const useRevolutionaryMarketData = (
  options: UseRevolutionaryMarketDataOptions = {}
): RevolutionaryMarketDataHook => {
  const {
    autoConnect = true,
    reconnectAttempts = 10,
    reconnectDelay = 2000,
    symbols = [],
    onError,
    onConnectionChange,
    onMarketData
  } = options

  // State
  const [marketData, setMarketData] = useState<Record<string, MarketData>>({})
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [subscriptions, setSubscriptions] = useState<Set<string>>(new Set())
  const [lastError, setLastError] = useState<string | null>(null)
  const [connectionStats, setConnectionStats] = useState({
    connectedAt: null as Date | null,
    reconnectCount: 0,
    messageCount: 0
  })

  // Refs
  const connectionManagerRef = useRef<RevolutionaryConnectionManager | null>(null)
  const subscriptionIdsRef = useRef<Map<string, string>>(new Map()) // symbol -> subscriptionId

  // Initialize connection manager
  useEffect(() => {
    if (!connectionManagerRef.current) {
      connectionManagerRef.current = RevolutionaryConnectionManager.getInstance(REVOLUTIONARY_SERVICE_URL)
    }
  }, [])

  // Sync connection status from manager
  useEffect(() => {
    const updateStatus = () => {
      if (connectionManagerRef.current) {
        const status = connectionManagerRef.current.getConnectionStatus()
        const stats = connectionManagerRef.current.getConnectionStats()
        setConnectionStatus(status)
        setConnectionStats(stats)
        onConnectionChange?.(status)
      }
    }

    const interval = setInterval(updateStatus, 1000) // Check status every second
    return () => clearInterval(interval)
  }, [onConnectionChange])

  // Connection management - remove onError and onConnectionChange from dependencies to prevent recreation
  const connect = useCallback(async () => {
    if (!connectionManagerRef.current) {
      connectionManagerRef.current = RevolutionaryConnectionManager.getInstance(REVOLUTIONARY_SERVICE_URL)
    }

    try {
      console.log('🚀 Connecting via shared connection manager...')
      setConnectionStatus('connecting')
      setLastError(null)
      await connectionManagerRef.current.connect()
      setConnectionStatus('connected')
      onConnectionChange?.('connected')
    } catch (error) {
      const errorMsg = `Connection failed: ${error}`
      console.error('❌ Revolutionary Service connection failed:', error)
      setConnectionStatus('error')
      setLastError(errorMsg)
      onError?.(errorMsg)
    }
  }, []) // Remove callback dependencies to prevent recreation

  // Disconnect
  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting from Revolutionary Service...')
    
    // Unsubscribe from all symbols first
    subscriptionIdsRef.current.forEach((subscriptionId, symbol) => {
      if (connectionManagerRef.current) {
        connectionManagerRef.current.unsubscribe(subscriptionId, symbol)
      }
    })
    subscriptionIdsRef.current.clear()
    
    setConnectionStatus('disconnected')
    setSubscriptions(new Set())
    setMarketData({})
  }, [])

  // Subscribe to symbol with enhanced validation and deduplication - remove onMarketData dependency
  const subscribe = useCallback((symbol: string) => {
    if (!symbol) {
      console.warn('⚠️ Cannot subscribe to empty symbol')
      return
    }

    if (!connectionManagerRef.current) {
      console.warn('⚠️ Connection manager not initialized')
      return
    }

    // Enhanced subscription deduplication
    if (subscriptionIdsRef.current.has(symbol)) {
      console.log(`📊 Already subscribed to ${symbol}, skipping duplicate subscription`)
      return
    }

    // Check if connection is ready
    if (connectionManagerRef.current.getConnectionStatus() !== 'connected') {
      console.log(`📊 Connection not ready for ${symbol}, will retry when connected`)
      return
    }

    console.log(`📊 Subscribing to ${symbol} via shared connection manager...`)
    
    // Subscribe via connection manager
    const subscriptionId = connectionManagerRef.current.subscribe(symbol, (data: MarketData) => {
      setMarketData(prev => ({
        ...prev,
        [data.symbol]: data
      }))
      
      setConnectionStats(prev => ({
        ...prev,
        messageCount: prev.messageCount + 1
      }))

      onMarketData?.(data)
    })

    // Store subscription ID
    subscriptionIdsRef.current.set(symbol, subscriptionId)
    
    // Add to local subscriptions
    setSubscriptions(prev => new Set([...prev, symbol]))
  }, []) // Remove callback dependencies to prevent recreation

  // Unsubscribe from symbol with enhanced cleanup and validation
  const unsubscribe = useCallback((symbol: string) => {
    if (!symbol) {
      console.warn('⚠️ Cannot unsubscribe from empty symbol')
      return
    }

    console.log(`📊 Unsubscribing from ${symbol}...`)
    
    const subscriptionId = subscriptionIdsRef.current.get(symbol)
    if (subscriptionId && connectionManagerRef.current) {
      connectionManagerRef.current.unsubscribe(subscriptionId, symbol)
      subscriptionIdsRef.current.delete(symbol)
      console.log(`📊 Successfully unsubscribed from ${symbol}`)
    } else {
      console.log(`📊 No active subscription found for ${symbol}`)
    }
    
    // Remove from local subscriptions atomically
    setSubscriptions(prev => {
      if (prev.has(symbol)) {
        const newSet = new Set(prev)
        newSet.delete(symbol)
        return newSet
      }
      return prev
    })
    
    // Remove from market data atomically
    setMarketData(prev => {
      if (prev[symbol]) {
        const newData = { ...prev }
        delete newData[symbol]
        return newData
      }
      return prev
    })
  }, [])

  // Auto-connect on mount - optimize with more specific dependencies
  useEffect(() => {
    console.log('🔄 useRevolutionaryMarketData mount effect:', { autoConnect, hasConnections: connectionManagerRef.current !== null })
    
    if (autoConnect) {
      connect()
    }

    // Cleanup on unmount
    return () => {
      console.log('🧹 useRevolutionaryMarketData cleanup - cleaning up subscriptions')
      // Clean up all subscriptions before disconnecting
      subscriptionIdsRef.current.forEach((subscriptionId, symbol) => {
        if (connectionManagerRef.current) {
          connectionManagerRef.current.unsubscribe(subscriptionId, symbol)
        }
      })
      subscriptionIdsRef.current.clear()
      disconnect()
    }
  }, [autoConnect]) // Remove connect and disconnect from dependencies to prevent recreation

  // Auto-subscribe to initial symbols with debouncing and deduplication - using ref for subscribe
  useEffect(() => {
    console.log('🔄 Symbol subscription effect:', { symbolsLength: symbols.length, connectionStatus, subscriptionsSize: subscriptions.size })
    
    if (symbols.length > 0 && connectionStatus === 'connected') {
      const newSymbols = symbols.filter(symbol => !subscriptions.has(symbol))
      
      if (newSymbols.length > 0) {
        console.log(`📊 Auto-subscribing to ${newSymbols.length} new symbols:`, newSymbols)
        
        // Use timeout to debounce rapid symbol changes
        const timeoutId = setTimeout(() => {
          newSymbols.forEach(symbol => {
            console.log(`🎯 Subscribing to symbol: ${symbol}`)
            // Call subscribe directly instead of using from dependency
            if (!connectionManagerRef.current) return
            
            if (subscriptionIdsRef.current.has(symbol)) return
            
            if (connectionManagerRef.current.getConnectionStatus() !== 'connected') return
            
            const subscriptionId = connectionManagerRef.current.subscribe(symbol, (data: MarketData) => {
              setMarketData(prev => ({
                ...prev,
                [data.symbol]: data
              }))
              
              setConnectionStats(prev => ({
                ...prev,
                messageCount: prev.messageCount + 1
              }))

              onMarketData?.(data)
            })

            subscriptionIdsRef.current.set(symbol, subscriptionId)
            setSubscriptions(prev => new Set([...prev, symbol]))
          })
        }, 100) // 100ms debounce
        
        return () => {
          console.log('🧹 Clearing subscription timeout')
          clearTimeout(timeoutId)
        }
      }
    }
  }, [symbols, connectionStatus, subscriptions]) // No function dependencies

  return {
    marketData,
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    subscribe,
    unsubscribe,
    connect,
    disconnect,
    subscriptions,
    lastError,
    connectionStats
  }
}

export default useRevolutionaryMarketData