import { supabase } from '@/lib/supabase/client'
import type { TradingAccount, Trade } from '@/types/database'

/**
 * Dashboard Service - Utility functions for calculating dashboard statistics
 */

export interface DashboardCalculations {
  totalBalance: number
  totalEquity: number
  dailyPnL: number
  weeklyPnL: number
  monthlyPnL: number
  winRate: number
  profitFactor: number
  averageWin: number
  averageLoss: number
  largestWin: number
  largestLoss: number
  totalTrades: number
  winningTrades: number
  losingTrades: number
  riskScore: number
}

export class DashboardService {
  /**
   * Calculate win rate from trades
   */
  static calculateWinRate(trades: Trade[]): number {
    const closedTrades = trades.filter(trade => trade.status === 'closed' && trade.profit !== null)
    if (closedTrades.length === 0) return 0
    
    const winningTrades = closedTrades.filter(trade => (trade.profit || 0) > 0)
    return (winningTrades.length / closedTrades.length) * 100
  }

  /**
   * Calculate daily P&L from trades
   */
  static calculateDailyPnL(trades: Trade[]): number {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    
    const todaysTrades = trades.filter(trade => {
      const tradeDate = new Date(trade.close_time || trade.open_time)
      return tradeDate >= today && tradeDate < tomorrow && trade.status === 'closed'
    })
    
    return todaysTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
  }

  /**
   * Calculate weekly P&L from trades
   */
  static calculateWeeklyPnL(trades: Trade[]): number {
    const weekAgo = new Date()
    weekAgo.setDate(weekAgo.getDate() - 7)
    weekAgo.setHours(0, 0, 0, 0)
    
    const weekTrades = trades.filter(trade => {
      const tradeDate = new Date(trade.close_time || trade.open_time)
      return tradeDate >= weekAgo && trade.status === 'closed'
    })
    
    return weekTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
  }

  /**
   * Calculate monthly P&L from trades
   */
  static calculateMonthlyPnL(trades: Trade[]): number {
    const monthAgo = new Date()
    monthAgo.setDate(monthAgo.getDate() - 30)
    monthAgo.setHours(0, 0, 0, 0)
    
    const monthTrades = trades.filter(trade => {
      const tradeDate = new Date(trade.close_time || trade.open_time)
      return tradeDate >= monthAgo && trade.status === 'closed'
    })
    
    return monthTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
  }

  /**
   * Calculate profit factor (total profit / total loss)
   */
  static calculateProfitFactor(trades: Trade[]): number {
    const closedTrades = trades.filter(trade => trade.status === 'closed' && trade.profit !== null)
    
    const totalProfit = closedTrades
      .filter(trade => (trade.profit || 0) > 0)
      .reduce((sum, trade) => sum + (trade.profit || 0), 0)
    
    const totalLoss = Math.abs(closedTrades
      .filter(trade => (trade.profit || 0) < 0)
      .reduce((sum, trade) => sum + (trade.profit || 0), 0))
    
    if (totalLoss === 0) {
      return totalProfit > 0 ? 999 : 0
    }
    
    return totalProfit / totalLoss
  }

  /**
   * Calculate average winning trade
   */
  static calculateAverageWin(trades: Trade[]): number {
    const winningTrades = trades.filter(trade => 
      trade.status === 'closed' && (trade.profit || 0) > 0
    )
    
    if (winningTrades.length === 0) return 0
    
    const totalProfit = winningTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
    return totalProfit / winningTrades.length
  }

  /**
   * Calculate average losing trade
   */
  static calculateAverageLoss(trades: Trade[]): number {
    const losingTrades = trades.filter(trade => 
      trade.status === 'closed' && (trade.profit || 0) < 0
    )
    
    if (losingTrades.length === 0) return 0
    
    const totalLoss = losingTrades.reduce((sum, trade) => sum + (trade.profit || 0), 0)
    return totalLoss / losingTrades.length
  }

  /**
   * Find largest winning trade
   */
  static calculateLargestWin(trades: Trade[]): number {
    const winningTrades = trades.filter(trade => 
      trade.status === 'closed' && (trade.profit || 0) > 0
    )
    
    if (winningTrades.length === 0) return 0
    
    return Math.max(...winningTrades.map(trade => trade.profit || 0))
  }

  /**
   * Find largest losing trade
   */
  static calculateLargestLoss(trades: Trade[]): number {
    const losingTrades = trades.filter(trade => 
      trade.status === 'closed' && (trade.profit || 0) < 0
    )
    
    if (losingTrades.length === 0) return 0
    
    return Math.min(...losingTrades.map(trade => trade.profit || 0))
  }

  /**
   * Calculate risk score based on account metrics
   */
  static calculateRiskScore(accounts: TradingAccount[]): number {
    if (accounts.length === 0) return 0
    
    let totalScore = 0
    let scoreCount = 0
    
    accounts.forEach(account => {
      // Drawdown risk (0-4 points, 4 being best)
      const maxDrawdownLimit = 10 // 10% typical max
      const currentDrawdown = account.drawdown || 0
      const drawdownScore = Math.max(0, 4 - (currentDrawdown / maxDrawdownLimit) * 4)
      
      // Account status (0-3 points)
      let statusScore = 0
      switch (account.status) {
        case 'active':
          statusScore = 3
          break
        case 'paused':
          statusScore = 1
          break
        case 'violation':
          statusScore = 0
          break
        default:
          statusScore = 2
      }
      
      // Phase progression (0-3 points)
      let phaseScore = 0
      switch (account.phase) {
        case 'funded':
          phaseScore = 3
          break
        case 'verification':
          phaseScore = 2
          break
        case 'evaluation':
          phaseScore = 1
          break
        default:
          phaseScore = 0
      }
      
      totalScore += drawdownScore + statusScore + phaseScore
      scoreCount += 1
    })
    
    if (scoreCount === 0) return 0
    
    // Convert to 0-10 scale
    const averageScore = totalScore / scoreCount
    const maxPossibleScore = 10 // 4 + 3 + 3
    
    return (averageScore / maxPossibleScore) * 10
  }

  /**
   * Calculate days left in challenge
   */
  static calculateDaysLeft(account: TradingAccount): number {
    if (!account.start_date || account.phase === 'funded') return 0
    
    // Different durations based on phase
    let challengeDuration = 30 // Default 30 days for evaluation
    if (account.phase === 'verification') {
      challengeDuration = 60 // 60 days for verification
    }
    
    const startDate = new Date(account.start_date)
    const currentDate = new Date()
    const daysElapsed = Math.floor((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    return Math.max(0, challengeDuration - daysElapsed)
  }

  /**
   * Format currency values
   */
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  /**
   * Format percentage values
   */
  static formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`
  }

  /**
   * Determine if a change is positive or negative
   */
  static getChangeDirection(current: number, previous: number): 'positive' | 'negative' | 'neutral' {
    if (current > previous) return 'positive'
    if (current < previous) return 'negative'
    return 'neutral'
  }

  /**
   * Get comprehensive dashboard calculations
   */
  static calculateAllStats(accounts: TradingAccount[], trades: Trade[]): DashboardCalculations {
    const closedTrades = trades.filter(trade => trade.status === 'closed')
    const winningTrades = closedTrades.filter(trade => (trade.profit || 0) > 0)
    const losingTrades = closedTrades.filter(trade => (trade.profit || 0) < 0)
    
    return {
      totalBalance: accounts.reduce((sum, acc) => sum + (acc.balance || 0), 0),
      totalEquity: accounts.reduce((sum, acc) => sum + (acc.equity || 0), 0),
      dailyPnL: this.calculateDailyPnL(trades),
      weeklyPnL: this.calculateWeeklyPnL(trades),
      monthlyPnL: this.calculateMonthlyPnL(trades),
      winRate: this.calculateWinRate(trades),
      profitFactor: this.calculateProfitFactor(trades),
      averageWin: this.calculateAverageWin(trades),
      averageLoss: this.calculateAverageLoss(trades),
      largestWin: this.calculateLargestWin(trades),
      largestLoss: this.calculateLargestLoss(trades),
      totalTrades: closedTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      riskScore: this.calculateRiskScore(accounts)
    }
  }
}

export const dashboardService = new DashboardService()