/**
 * usePositionTracking Hook
 * Provides real-time position tracking and P&L calculations
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { supabase } from '@/lib/supabase/client'
import { marketDataLookup } from '@/lib/services/marketDataLookupService'
import type { Trade } from '@/types/database'

export interface Position extends Trade {
  current_price?: number
  unrealized_pl?: number
  unrealized_pl_percentage?: number
  price_change?: number
  price_change_percentage?: number
  is_profitable?: boolean
}

export interface PositionSummary {
  totalUnrealizedPL: number
  totalUnrealizedPLPercentage: number
  profitablePositions: number
  losingPositions: number
  totalPositions: number
  largestGain: number
  largestLoss: number
}

interface UsePositionTrackingOptions {
  accountId?: string
  autoRefresh?: boolean
  refreshInterval?: number
  tradingViewConnected?: boolean // Use TradingView connection status instead of separate WebSocket
  enableRealTimeUpdates?: boolean // Enable immediate position updates on price changes
}

export const usePositionTracking = (options: UsePositionTrackingOptions = {}) => {
  const {
    accountId,
    autoRefresh = true,
    refreshInterval = 1000, // 1 second
    tradingViewConnected = false,
    enableRealTimeUpdates = true // Enable real-time updates by default
  } = options

  // State
  const [positions, setPositions] = useState<Position[]>([])
  const [positionSummary, setPositionSummary] = useState<PositionSummary>({
    totalUnrealizedPL: 0,
    totalUnrealizedPLPercentage: 0,
    profitablePositions: 0,
    losingPositions: 0,
    totalPositions: 0,
    largestGain: 0,
    largestLoss: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // Refs
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const isUpdatingRef = useRef(false)
  const subscriberId = useRef(`position-tracking-${accountId || 'global'}-${Date.now()}`)
  const priceUpdateUnsubscribers = useRef<Map<string, () => void>>(new Map())

  // Use TradingView connection status instead of separate WebSocket
  const isConnected = tradingViewConnected

  /**
   * Fetch open positions from database
   */
  const fetchPositions = useCallback(async () => {
    if (isUpdatingRef.current) return

    try {
      isUpdatingRef.current = true
      setError(null)

      let query = supabase
        .from('trades')
        .select('*')
        .eq('status', 'open')
        .order('open_time', { ascending: false })

      if (accountId) {
        query = query.eq('account_id', accountId)
      }

      const { data, error: fetchError } = await query

      if (fetchError) {
        throw new Error(fetchError.message)
      }

      console.log(`📊 [usePositionTracking] Fetched ${data?.length || 0} open positions`)
      return data || []

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch positions'
      console.error('📊 [usePositionTracking] Fetch error:', errorMessage)
      setError(errorMessage)
      return []
    } finally {
      isUpdatingRef.current = false
    }
  }, [accountId])

  /**
   * Calculate P&L for all positions using current market data
   */
  const calculatePositionPL = useCallback(async (trades: Trade[]): Promise<Position[]> => {
    if (trades.length === 0) return []

    console.log(`📊 [usePositionTracking] Calculating P&L for ${trades.length} positions`)

    const enrichedPositions: Position[] = await Promise.all(
      trades.map(async (trade): Promise<Position> => {
        try {
          // Get current market price for P&L calculation
          const currentPrice = await marketDataLookup.getCurrentPrice(trade.symbol, trade.type as 'buy' | 'sell')
          
          // Calculate unrealized P&L
          const unrealizedPL = await marketDataLookup.calculateUnrealizedPL(
            trade.symbol,
            trade.open_price,
            trade.volume,
            trade.type as 'buy' | 'sell'
          )

          // Calculate price change from entry
          const priceChange = currentPrice - trade.open_price
          const priceChangePercentage = (priceChange / trade.open_price) * 100

          // Calculate P&L percentage based on position value
          const positionValue = trade.open_price * trade.volume
          const unrealizedPLPercentage = positionValue > 0 ? (unrealizedPL / positionValue) * 100 : 0

          const position: Position = {
            ...trade,
            current_price: currentPrice,
            unrealized_pl: unrealizedPL,
            unrealized_pl_percentage: parseFloat(unrealizedPLPercentage.toFixed(4)),
            price_change: parseFloat(priceChange.toFixed(5)),
            price_change_percentage: parseFloat(priceChangePercentage.toFixed(4)),
            is_profitable: unrealizedPL > 0
          }

          return position

        } catch (error) {
          console.error(`📊 [usePositionTracking] Error calculating P&L for ${trade.symbol}:`, error)
          
          // Return position with minimal data if calculation fails
          return {
            ...trade,
            current_price: trade.open_price,
            unrealized_pl: 0,
            unrealized_pl_percentage: 0,
            price_change: 0,
            price_change_percentage: 0,
            is_profitable: false
          }
        }
      })
    )

    return enrichedPositions
  }, [])

  /**
   * Calculate position summary statistics
   */
  const calculateSummary = useCallback((positions: Position[]): PositionSummary => {
    if (positions.length === 0) {
      return {
        totalUnrealizedPL: 0,
        totalUnrealizedPLPercentage: 0,
        profitablePositions: 0,
        losingPositions: 0,
        totalPositions: 0,
        largestGain: 0,
        largestLoss: 0
      }
    }

    const totalUnrealizedPL = positions.reduce((sum, pos) => sum + (pos.unrealized_pl || 0), 0)
    const profitablePositions = positions.filter(pos => (pos.unrealized_pl || 0) > 0).length
    const losingPositions = positions.filter(pos => (pos.unrealized_pl || 0) < 0).length
    
    const allPLValues = positions.map(pos => pos.unrealized_pl || 0)
    const largestGain = allPLValues.length > 0 ? Math.max(...allPLValues) : 0
    const largestLoss = allPLValues.length > 0 ? Math.min(...allPLValues) : 0

    // Calculate total invested amount for percentage calculation
    const totalInvested = positions.reduce((sum, pos) => sum + (pos.open_price * pos.volume), 0)
    const totalUnrealizedPLPercentage = totalInvested > 0 ? (totalUnrealizedPL / totalInvested) * 100 : 0

    return {
      totalUnrealizedPL: parseFloat(totalUnrealizedPL.toFixed(2)),
      totalUnrealizedPLPercentage: parseFloat(totalUnrealizedPLPercentage.toFixed(4)),
      profitablePositions,
      losingPositions,
      totalPositions: positions.length,
      largestGain: parseFloat(largestGain.toFixed(2)),
      largestLoss: parseFloat(largestLoss.toFixed(2))
    }
  }, [])

  /**
   * Real-time position recalculation when prices change
   * This is called immediately when new price data arrives
   */
  const recalculatePositionPL = useCallback(async (updatedSymbol: string, newPrice: any) => {
    if (!enableRealTimeUpdates) return
    
    console.log(`🔔 [usePositionTracking] Real-time price update for ${updatedSymbol}:`, newPrice)
    
    // Only recalculate positions for the updated symbol to optimize performance
    setPositions(currentPositions => {
      const updatedPositions = currentPositions.map(position => {
        if (position.symbol.toUpperCase() !== updatedSymbol.toUpperCase()) {
          return position // No change for other symbols
        }
        
        try {
          // Calculate new P&L with updated price
          const currentPrice = position.type === 'buy' ? newPrice.bid : newPrice.ask
          const priceDiff = currentPrice - position.open_price
          const direction = position.type === 'buy' ? 1 : -1
          
          // Use symbol-specific lot size
          const symbolSpecs = marketDataLookup.getSymbolSpecs && marketDataLookup.getSymbolSpecs(position.symbol)
          const lotSize = symbolSpecs?.lotSize || 100000 // Default forex lot size
          
          const unrealizedPL = priceDiff * position.volume * direction * lotSize
          const positionValue = position.open_price * position.volume * lotSize
          const unrealizedPLPercentage = positionValue > 0 ? (unrealizedPL / positionValue) * 100 : 0
          
          const priceChange = currentPrice - position.open_price
          const priceChangePercentage = (priceChange / position.open_price) * 100
          
          const updatedPosition: Position = {
            ...position,
            current_price: currentPrice,
            unrealized_pl: parseFloat(unrealizedPL.toFixed(2)),
            unrealized_pl_percentage: parseFloat(unrealizedPLPercentage.toFixed(4)),
            price_change: parseFloat(priceChange.toFixed(5)),
            price_change_percentage: parseFloat(priceChangePercentage.toFixed(4)),
            is_profitable: unrealizedPL > 0
          }
          
          console.log(`📊 [usePositionTracking] Real-time P&L update: ${position.symbol} P&L: ${unrealizedPL.toFixed(2)}`)
          return updatedPosition
          
        } catch (error) {
          console.error(`❌ [usePositionTracking] Error in real-time P&L calculation for ${position.symbol}:`, error)
          return position // Return unchanged on error
        }
      })
      
      // Recalculate summary with updated positions
      const newSummary = calculateSummary(updatedPositions)
      setPositionSummary(newSummary)
      setLastUpdate(new Date())
      
      return updatedPositions
    })
  }, [enableRealTimeUpdates, calculateSummary])

  /**
   * Update symbol subscriptions based on current positions
   * Enhanced with real-time price update callbacks
   */
  const updateSymbolSubscriptions = useCallback((trades: Trade[]) => {
    const activeSymbols = [...new Set(trades.map(trade => trade.symbol.toUpperCase()))]
    const currentSubscriptions = marketDataLookup.getActiveSymbols()
    
    // Clean up old price update subscriptions
    priceUpdateUnsubscribers.current.forEach((unsubscribe, symbol) => {
      if (!activeSymbols.includes(symbol)) {
        unsubscribe()
        priceUpdateUnsubscribers.current.delete(symbol)
        console.log(`📊 [usePositionTracking] Removed price update callback for ${symbol}`)
      }
    })
    
    // Subscribe to new symbols with real-time callbacks
    activeSymbols.forEach(symbol => {
      marketDataLookup.subscribeToSymbol(symbol, subscriberId.current)
      
      // Add real-time price update callback if not already subscribed
      if (enableRealTimeUpdates && !priceUpdateUnsubscribers.current.has(symbol)) {
        const unsubscribe = marketDataLookup.onPriceUpdate(symbol, (price) => {
          recalculatePositionPL(symbol, price)
        })
        priceUpdateUnsubscribers.current.set(symbol, unsubscribe)
        console.log(`📞 [usePositionTracking] Added real-time price callback for ${symbol}`)
      }
    })
    
    // Unsubscribe from symbols no longer needed (only our subscriptions)
    const symbolsToUnsubscribe = currentSubscriptions.filter(symbol => 
      !activeSymbols.includes(symbol)
    )
    
    symbolsToUnsubscribe.forEach(symbol => {
      marketDataLookup.unsubscribeFromSymbol(symbol, subscriberId.current)
    })
    
    console.log(`📊 [usePositionTracking] Updated symbol subscriptions: ${activeSymbols.join(', ')}`)
    console.log(`📊 [usePositionTracking] Real-time callbacks: ${enableRealTimeUpdates ? 'ENABLED' : 'DISABLED'}`)
    console.log(`📊 [usePositionTracking] Total active symbols in market data lookup:`, marketDataLookup.getActiveSymbols())
  }, [enableRealTimeUpdates, recalculatePositionPL])

  /**
   * Update positions with latest market data
   */
  const updatePositions = useCallback(async () => {
    try {
      setLoading(true)
      
      // Fetch latest positions from database
      const trades = await fetchPositions()
      
      // Update symbol subscriptions for all active positions
      updateSymbolSubscriptions(trades)
      
      // Calculate P&L for all positions
      const enrichedPositions = await calculatePositionPL(trades)
      
      // Update positions state
      setPositions(enrichedPositions)
      
      // Calculate and update summary
      const summary = calculateSummary(enrichedPositions)
      setPositionSummary(summary)
      
      setLastUpdate(new Date())
      
      console.log(`📊 [usePositionTracking] Updated ${enrichedPositions.length} positions, Total P&L: ${summary.totalUnrealizedPL}`)

    } catch (error) {
      console.error('📊 [usePositionTracking] Update error:', error)
      setError(error instanceof Error ? error.message : 'Failed to update positions')
    } finally {
      setLoading(false)
    }
  }, [fetchPositions, calculatePositionPL, calculateSummary, updateSymbolSubscriptions])

  /**
   * Get position by trade ID
   */
  const getPosition = useCallback((tradeId: string): Position | undefined => {
    return positions.find(pos => pos.id === tradeId)
  }, [positions])

  /**
   * Get positions for a specific symbol
   */
  const getPositionsBySymbol = useCallback((symbol: string): Position[] => {
    return positions.filter(pos => pos.symbol === symbol)
  }, [positions])

  /**
   * Refresh positions manually
   */
  const refresh = useCallback(() => {
    console.log('📊 [usePositionTracking] Manual refresh triggered')
    updatePositions()
  }, [updatePositions])

  // Update market data lookup service based on TradingView connection
  useEffect(() => {
    marketDataLookup.setWebSocketStatus(tradingViewConnected)
  }, [tradingViewConnected])

  // Set up auto-refresh interval
  useEffect(() => {
    if (autoRefresh) {
      console.log(`📊 [usePositionTracking] Setting up auto-refresh every ${refreshInterval}ms`)
      
      refreshIntervalRef.current = setInterval(() => {
        updatePositions()
      }, refreshInterval)

      // Initial update
      updatePositions()

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current)
          refreshIntervalRef.current = null
        }
      }
    } else {
      // Manual mode - just do initial update
      updatePositions()
    }
  }, [autoRefresh, refreshInterval, updatePositions])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
        refreshIntervalRef.current = null
      }
      
      // Clean up real-time price update callbacks
      priceUpdateUnsubscribers.current.forEach((unsubscribe, symbol) => {
        unsubscribe()
        console.log(`📞 [usePositionTracking] Cleaned up price callback for ${symbol}`)
      })
      priceUpdateUnsubscribers.current.clear()
      
      // Unsubscribe from all symbols for this instance
      const activeSymbols = marketDataLookup.getActiveSymbols()
      activeSymbols.forEach(symbol => {
        marketDataLookup.unsubscribeFromSymbol(symbol, subscriberId.current)
      })
      
      console.log(`📊 [usePositionTracking] Cleaned up all subscriptions for ${subscriberId.current}`)
    }
  }, [])

  return {
    // Data
    positions,
    positionSummary,
    
    // Status
    loading,
    error,
    lastUpdate,
    isConnected,
    hasLiveData: tradingViewConnected,
    realTimeEnabled: enableRealTimeUpdates,
    
    // Actions
    refresh,
    updatePositions,
    
    // Helpers
    getPosition,
    getPositionsBySymbol,
    
    // Market data info
    marketDataStatus: {
      connected: tradingViewConnected,
      symbolCount: 0, // Will be updated by TradingView data
      lastMarketUpdate: null
    }
  }
}

export default usePositionTracking