'use client'

/**
 * WebSocket Error Boundary
 * 
 * Provides comprehensive error handling for WebSocket-related components.
 * Isolates WebSocket failures from causing UI crashes.
 */

import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, Wifi, WifiOff, Database, Shield } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  retryCount: number
}

export class WebSocketErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('📊 [WebSocketErrorBoundary] Caught error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  handleRetry = () => {
    console.log('🔄 [WebSocketErrorBoundary] Retrying after error')
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }))
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 m-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800">
                WebSocket Connection Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>There was a problem with the real-time data connection.</p>
                {this.state.error && (
                  <details className="mt-2">
                    <summary className="cursor-pointer font-medium">
                      Error Details
                    </summary>
                    <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto">
                      {this.state.error.message}
                      {this.state.errorInfo && (
                        <>
                          {'\n\nComponent Stack:'}
                          {this.state.errorInfo.componentStack}
                        </>
                      )}
                    </pre>
                  </details>
                )}
              </div>
              <div className="mt-4">
                <button
                  onClick={this.handleRetry}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Connection
                </button>
                {this.state.retryCount > 0 && (
                  <span className="ml-3 text-xs text-red-600">
                    Retry attempts: {this.state.retryCount}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Connection Status Indicator
 * 
 * Displays current WebSocket connection status with visual indicators
 */

export interface ConnectionStatus {
  state: 'connected' | 'connecting' | 'disconnected' | 'reconnecting' | 'failed'
  isUsingFallback: boolean
  lastError?: string
  reconnectAttempt?: number
  maxReconnects?: number
}

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus
  onRetry?: () => void
  className?: string
}

export const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  status,
  onRetry,
  className = ''
}) => {
  const getStatusIcon = () => {
    switch (status.state) {
      case 'connected':
        return status.isUsingFallback 
          ? <Database className="h-4 w-4 text-yellow-500" />
          : <Wifi className="h-4 w-4 text-green-500" />
      case 'connecting':
      case 'reconnecting':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'disconnected':
      case 'failed':
        return <WifiOff className="h-4 w-4 text-red-500" />
      default:
        return <WifiOff className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = () => {
    switch (status.state) {
      case 'connected':
        return status.isUsingFallback 
          ? 'bg-yellow-50 border-yellow-200'
          : 'bg-green-50 border-green-200'
      case 'connecting':
      case 'reconnecting':
        return 'bg-blue-50 border-blue-200'
      case 'disconnected':
      case 'failed':
        return 'bg-red-50 border-red-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getStatusText = () => {
    switch (status.state) {
      case 'connected':
        return status.isUsingFallback 
          ? 'Connected (Fallback Data)'
          : 'Connected (Live Data)'
      case 'connecting':
        return 'Connecting...'
      case 'reconnecting':
        return `Reconnecting... (${status.reconnectAttempt}/${status.maxReconnects})`
      case 'disconnected':
        return 'Disconnected'
      case 'failed':
        return 'Connection Failed'
      default:
        return 'Unknown Status'
    }
  }

  const getStatusDescription = () => {
    switch (status.state) {
      case 'connected':
        return status.isUsingFallback 
          ? 'Using cached or simulated market data'
          : 'Receiving live market data'
      case 'connecting':
        return 'Establishing connection to market data'
      case 'reconnecting':
        return 'Attempting to restore connection'
      case 'disconnected':
        return 'No connection to market data'
      case 'failed':
        return status.lastError || 'Unable to connect to market data'
      default:
        return ''
    }
  }

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${getStatusColor()} ${className}`}>
      <div className="flex-shrink-0">
        {getStatusIcon()}
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900">
          {getStatusText()}
        </div>
        <div className="text-xs text-gray-600">
          {getStatusDescription()}
        </div>
      </div>
      {(status.state === 'failed' || status.state === 'disconnected') && onRetry && (
        <button
          onClick={onRetry}
          className="flex-shrink-0 inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <RefreshCw className="h-3 w-3 mr-1" />
          Retry
        </button>
      )}
    </div>
  )
}

/**
 * Data Quality Indicator
 * 
 * Shows the quality and source of current market data
 */

export interface DataQuality {
  source: 'live' | 'cached' | 'mock'
  freshness: 'fresh' | 'stale' | 'very_stale'
  lastUpdate: number
  quality: 'high' | 'medium' | 'low'
}

interface DataQualityIndicatorProps {
  quality: DataQuality
  className?: string
}

export const DataQualityIndicator: React.FC<DataQualityIndicatorProps> = ({
  quality,
  className = ''
}) => {
  const getQualityIcon = () => {
    switch (quality.source) {
      case 'live':
        return <Wifi className="h-3 w-3 text-green-500" />
      case 'cached':
        return <Database className="h-3 w-3 text-yellow-500" />
      case 'mock':
        return <Shield className="h-3 w-3 text-blue-500" />
      default:
        return <WifiOff className="h-3 w-3 text-gray-400" />
    }
  }

  const getQualityColor = () => {
    switch (quality.quality) {
      case 'high':
        return 'text-green-600 bg-green-50'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50'
      case 'low':
        return 'text-blue-600 bg-blue-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getSourceLabel = () => {
    switch (quality.source) {
      case 'live':
        return 'Live'
      case 'cached':
        return 'Cached'
      case 'mock':
        return 'Demo'
      default:
        return 'Unknown'
    }
  }

  const getLastUpdateText = () => {
    const now = Date.now()
    const diff = now - quality.lastUpdate
    
    if (diff < 5000) return 'Just now'
    if (diff < 60000) return `${Math.floor(diff / 1000)}s ago`
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
    return `${Math.floor(diff / 3600000)}h ago`
  }

  return (
    <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium ${getQualityColor()} ${className}`}>
      {getQualityIcon()}
      <span>{getSourceLabel()}</span>
      <span className="text-gray-500">•</span>
      <span>{getLastUpdateText()}</span>
    </div>
  )
}

/**
 * Comprehensive WebSocket Status Panel
 * 
 * Combines connection status, data quality, and error information
 */

interface WebSocketStatusPanelProps {
  connectionStatus: ConnectionStatus
  dataQuality?: DataQuality
  metrics?: {
    totalConnections: number
    successfulConnections: number
    failedConnections: number
    uptime: number
  }
  onRetry?: () => void
  onToggleDetails?: () => void
  showDetails?: boolean
  className?: string
}

export const WebSocketStatusPanel: React.FC<WebSocketStatusPanelProps> = ({
  connectionStatus,
  dataQuality,
  metrics,
  onRetry,
  onToggleDetails,
  showDetails = false,
  className = ''
}) => {
  const formatUptime = (uptime: number) => {
    const seconds = Math.floor(uptime / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ConnectionStatusIndicator 
              status={connectionStatus} 
              onRetry={onRetry}
            />
            {dataQuality && (
              <DataQualityIndicator quality={dataQuality} />
            )}
          </div>
          {onToggleDetails && (
            <button
              onClick={onToggleDetails}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          )}
        </div>
        
        {showDetails && metrics && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Total Connections:</span>
                <span className="ml-2 font-medium">{metrics.totalConnections}</span>
              </div>
              <div>
                <span className="text-gray-500">Success Rate:</span>
                <span className="ml-2 font-medium">
                  {metrics.totalConnections > 0 
                    ? Math.round((metrics.successfulConnections / metrics.totalConnections) * 100)
                    : 0}%
                </span>
              </div>
              <div>
                <span className="text-gray-500">Failed Connections:</span>
                <span className="ml-2 font-medium">{metrics.failedConnections}</span>
              </div>
              <div>
                <span className="text-gray-500">Uptime:</span>
                <span className="ml-2 font-medium">{formatUptime(metrics.uptime)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}