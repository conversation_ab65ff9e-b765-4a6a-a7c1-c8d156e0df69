'use client'

import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react'
import { 
  create<PERSON><PERSON>, 
  IChartApi, 
  CandlestickData,
  ColorType,
  CrosshairMode,
  CandlestickSeries
} from 'lightweight-charts'
import { ChartErrorBoundary, useChartErrorHandler } from './ChartErrorBoundary'

interface ChartData {
  time: string | number
  open: number
  high: number
  low: number
  close: number
}

interface CustomTradingChartProps {
  data: ChartData[]
  symbol: string
  interval: string
  height?: number
  onCrosshairMove?: (param: any) => void
  chartDecimals?: number
  onLoadMoreHistory?: (fromTime: number, toTime: number, currentDataLength: number) => Promise<ChartData[]>
  onTimeRangeChange?: (from: number, to: number) => void
  isLoadingHistory?: boolean
  showManualLoadButton?: boolean
}

export interface ChartMethods {
  updateData: (newData: ChartData[]) => void
  addData: (dataPoint: ChartData) => void
  updateCurrentCandle: (candleData: ChartData) => void
  addPriceLine: (price: number, color?: string, title?: string) => any
  removePriceLine: (priceLine: any) => void
  fitContent: () => void
  getChart: () => IChartApi | null
  prependHistoricalData: (historicalData: ChartData[]) => void
  getVisibleTimeRange: () => { from: number, to: number } | null
  getCurrentDataRange: () => { earliest: number, latest: number } | null
  scrollToRealtime: () => void
  // PHASE 1: New scroll state management methods
  enableAutoScroll: () => void
  disableAutoScroll: () => void
  isViewingHistoricalData: () => boolean
  getScrollState: () => {
    isViewingHistoricalData: boolean
    isUserScrolling: boolean
    autoScrollEnabled: boolean
  }
}

const CustomTradingChart = forwardRef<ChartMethods, CustomTradingChartProps>(
  ({ 
    data, 
    symbol, 
    interval, 
    height = 400, 
    onCrosshairMove, 
    chartDecimals = 3, 
    onLoadMoreHistory, 
    onTimeRangeChange,
    isLoadingHistory = false,
    showManualLoadButton = true
  }, ref) => {
    const chartContainerRef = useRef<HTMLDivElement>(null)
    const chartRef = useRef<IChartApi | null>(null)
    const seriesRef = useRef<any>(null)
    const allDataRef = useRef<ChartData[]>([])
    const isLoadingHistoryRef = useRef(false)
    const lastHistoryRequestRef = useRef<number>(0)
    const pendingHistoryRequestRef = useRef<string | null>(null)
    const showManualLoadRef = useRef(false)
    const [showManualLoad, setShowManualLoad] = useState(false)
    const [showHistoricalIndicator, setShowHistoricalIndicator] = useState(false)
    const [showJumpToLiveButton, setShowJumpToLiveButton] = useState(false)
    // ✅ FRONTEND INDEPENDENCE: Chart state management
    const [chartReady, setChartReady] = useState(false)
    const [chartError, setChartError] = useState<string | null>(null)
    // ✅ ERROR BOUNDARY: Error handling hook
    const { error: boundaryError, handleError, resetError } = useChartErrorHandler()
    const lastVisibleFromRef = useRef<number>(0)
    const isScrollingBackwardRef = useRef(false)
    const isUserInteractingRef = useRef(false)
    const interactionTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    
    // PHASE 1: Scroll state tracking to detect user navigation vs auto-updates
    const isUserScrollingRef = useRef<boolean>(false)
    const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const userScrollStartTimeRef = useRef<number>(0)
    const isViewingHistoricalDataRef = useRef<boolean>(false)
    const autoScrollToLiveEnabledRef = useRef<boolean>(true)

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      updateData: (newData: ChartData[]) => {
        if (seriesRef.current && newData.length > 0) {
          // PERFORMANCE FIX: Only use setData() for initial bulk data loading
          // This method should only be called during initial chart setup
          const formattedData = formatData(newData)
          
          // Validate data before calling setData
          if (formattedData.length === 0) {
            console.warn('📊 [CustomTradingChart] No valid data after formatting, skipping setData')
            return () => {}
          }
          
          // Extra safety check for time ordering
          for (let i = 1; i < formattedData.length; i++) {
            const prevTime = formattedData[i - 1].time as number
            const currTime = formattedData[i].time as number
            if (currTime <= prevTime) {
              console.error('📊 [CustomTradingChart] CRITICAL: Time ordering violation before setData!', {
                index: i,
                prevTime,
                currTime,
                prevDate: new Date(prevTime * 1000).toISOString(),
                currDate: new Date(currTime * 1000).toISOString()
              })
              return () => {} // Abort to prevent LightweightCharts error
            }
          }
          
          try {
            seriesRef.current.setData(formattedData)
            console.log('📊 [CustomTradingChart] Updated chart with bulk data:', formattedData.length, 'candles')
          } catch (error) {
            console.error('📊 [CustomTradingChart] Error in setData:', error)
            console.log('📊 [CustomTradingChart] Formatted data sample:', formattedData.slice(0, 5))
            if (error instanceof Error) {
              handleError(error)
            }
          }
        }
      },
      addData: (dataPoint: ChartData) => {
        if (seriesRef.current) {
          // PHASE 1: Enhanced interaction and scroll state detection
          if (isUserInteractingRef.current || isUserScrollingRef.current) {
            console.log('⏸️ [CustomTradingChart] Skipping data update during user interaction/scrolling')
            return () => {}
          }
          
          // PHASE 1: Check if user is viewing historical data to prevent auto-zoom
          if (isViewingHistoricalDataRef.current && !autoScrollToLiveEnabledRef.current) {
            console.log('📜 [CustomTradingChart] User viewing historical data - preserving scroll position')
            
            // Still update the data but don't force scroll to live edge
            try {
              const formatted = formatSingleDataPoint(dataPoint)
              if (formatted) {
                seriesRef.current.update(formatted)
                allDataRef.current = [...allDataRef.current, dataPoint]
                console.log('📊 [CustomTradingChart] Updated data without auto-zoom:', formatted)
              }
            } catch (error) {
              console.error('❌ [CustomTradingChart] Error updating data in historical view:', error)
            }
            return () => {}
          }
          
          try {
            const formatted = formatSingleDataPoint(dataPoint)
            if (!formatted) {
              console.warn('📊 [CustomTradingChart] Invalid data point, skipping addData')
              return () => {}
            }
            
            // Validate time ordering against existing data
            if (allDataRef.current.length > 0) {
              const lastCandle = allDataRef.current[allDataRef.current.length - 1]
              const lastTime = typeof lastCandle.time === 'string' 
                ? new Date(lastCandle.time).getTime() / 1000 
                : lastCandle.time
              const newTime = formatted.time as number
              
              if (newTime < lastTime) {
                console.warn('📊 [CustomTradingChart] Time ordering violation in addData:', {
                  lastTime,
                  newTime,
                  lastDate: new Date(lastTime * 1000).toISOString(),
                  newDate: new Date(newTime * 1000).toISOString()
                })
                return () => {} // Skip this data point to maintain order
              }
            }
            
            // Pure series.update() - TradingView handles new vs existing candles automatically
            seriesRef.current.update(formatted)
            
            // PHASE 1: Enhanced data management without artificial limits
            allDataRef.current = [...allDataRef.current, dataPoint]
            // PHASE 1: Removed 1000 candle limit to allow unlimited historical scrolling
            // Memory management will be handled by browser's natural limits
            
            console.log('📊 [CustomTradingChart] Updated series with candle:', formatted)
          } catch (error) {
            console.error('❌ [CustomTradingChart] Error adding data point:', error)
          }
        }
      },
      updateCurrentCandle: (candleData: ChartData) => {
        if (seriesRef.current) {
          // PHASE 1: Enhanced interaction and scroll state detection
          if (isUserInteractingRef.current || isUserScrollingRef.current) {
            console.log('⏸️ [CustomTradingChart] Skipping candle update during user interaction/scrolling')
            return () => {}
          }
          
          // PHASE 1: Check if user is viewing historical data to prevent auto-zoom
          if (isViewingHistoricalDataRef.current && !autoScrollToLiveEnabledRef.current) {
            console.log('📜 [CustomTradingChart] User viewing historical data - preserving scroll position for candle update')
            
            // Still update the current candle but don't force scroll to live edge
            try {
              const formatted = formatSingleDataPoint(candleData)
              if (formatted) {
                seriesRef.current.update(formatted)
                
                // Update the last candle in internal data reference
                if (allDataRef.current.length > 0) {
                  const lastIndex = allDataRef.current.length - 1
                  const lastTime = typeof allDataRef.current[lastIndex].time === 'string'
                    ? new Date(allDataRef.current[lastIndex].time).getTime() / 1000
                    : allDataRef.current[lastIndex].time
                  const updateTime = formatted.time as number
                  
                  if (Math.abs(lastTime - updateTime) < 1) {
                    allDataRef.current[lastIndex] = candleData
                  }
                }
                
                console.log('📊 [CustomTradingChart] Updated current candle without auto-zoom:', formatted)
              }
            } catch (error) {
              console.error('❌ [CustomTradingChart] Error updating candle in historical view:', error)
            }
            return () => {}
          }
          
          try {
            const formatted = formatSingleDataPoint(candleData)
            if (!formatted) {
              console.warn('📊 [CustomTradingChart] Invalid candle data, skipping updateCurrentCandle')
              return () => {}
            }
            
            // Validate time ordering for current candle update
            if (allDataRef.current.length > 0) {
              const lastCandle = allDataRef.current[allDataRef.current.length - 1]
              const lastTime = typeof lastCandle.time === 'string' 
                ? new Date(lastCandle.time).getTime() / 1000 
                : lastCandle.time
              const updateTime = formatted.time as number
              
              // For current candle updates, allow same time (update) but not earlier time
              if (updateTime < lastTime) {
                console.warn('📊 [CustomTradingChart] Time ordering violation in updateCurrentCandle:', {
                  lastTime,
                  updateTime,
                  lastDate: new Date(lastTime * 1000).toISOString(),
                  updateDate: new Date(updateTime * 1000).toISOString()
                })
                return () => {} // Skip this update to maintain order
              }
            }
            
            // Pure series.update() - same method for both updates and new candles
            seriesRef.current.update(formatted)
            
            // HYBRID OPTIMIZATION: Update the last candle in internal data reference
            if (allDataRef.current.length > 0) {
              const lastIndex = allDataRef.current.length - 1
              const lastTime = typeof allDataRef.current[lastIndex].time === 'string'
                ? new Date(allDataRef.current[lastIndex].time).getTime() / 1000
                : allDataRef.current[lastIndex].time
              const updateTime = formatted.time as number
              
              if (Math.abs(lastTime - updateTime) < 1) { // Same candle (within 1 second tolerance)
                allDataRef.current[lastIndex] = candleData
              }
            }
            
            console.log('📊 [CustomTradingChart] Updated current candle:', formatted)
          } catch (error) {
            console.error('❌ [CustomTradingChart] Error updating current candle:', error)
          }
        }
      },
      addPriceLine: (price: number, color: string = '#00ff00', title: string = 'Current Price') => {
        if (seriesRef.current) {
          return seriesRef.current.createPriceLine({
            price: price,
            color: color,
            lineWidth: 2,
            lineStyle: 2, // dashed
            axisLabelVisible: true,
            title: title
          })
        }
        return null
      },
      removePriceLine: (priceLine: any) => {
        if (seriesRef.current && priceLine) {
          seriesRef.current.removePriceLine(priceLine)
        }
      },
      fitContent: () => {
        if (chartRef.current) {
          chartRef.current.timeScale().fitContent()
        }
      },
      getChart: () => chartRef.current,
      prependHistoricalData: (historicalData: ChartData[]) => {
        if (seriesRef.current && historicalData.length > 0) {
          console.log(`📊 [CustomTradingChart] Prepending ${historicalData.length} historical candles`)
          
          // PHASE 2 FIX: Use individual update() calls to avoid chart reset
          // Save current visible range to restore it after updates
          const currentVisibleRange = chartRef.current?.timeScale().getVisibleRange()
          
          // Merge historical data with existing data
          const currentData = allDataRef.current
          const mergedData = [...historicalData, ...currentData]
          
          // Optimized duplicate removal using Map for better performance with large datasets
          const uniqueDataMap = new Map<string | number, ChartData>()
          
          mergedData.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString()
            if (!uniqueDataMap.has(timeKey) || uniqueDataMap.get(timeKey)!.time < item.time) {
              uniqueDataMap.set(timeKey, item)
            }
          })
          
          // Convert back to array and sort
          const uniqueData = Array.from(uniqueDataMap.values()).sort((a, b) => {
            const timeA = typeof a.time === 'string' ? new Date(a.time).getTime() : a.time
            const timeB = typeof b.time === 'string' ? new Date(b.time).getTime() : b.time
            return timeA - timeB
          })
          
          // Update internal data reference
          allDataRef.current = uniqueData
          
          // PHASE 2 OPTIMIZATION: Only use setData() if we have significantly more historical data
          // Otherwise use individual updates to prevent scroll reset
          const newHistoricalCount = historicalData.length
          const totalDataCount = uniqueData.length
          
          if (newHistoricalCount > 100 || totalDataCount < 50) {
            // Use setData() for large historical loads or small datasets where reset is acceptable
            console.log(`📊 [CustomTradingChart] Using setData() for large historical load (${newHistoricalCount} new candles)`)
            const formattedData = formatData(uniqueData)
            seriesRef.current.setData(formattedData)
            
            // Restore visible range to prevent user disorientation
            if (currentVisibleRange && chartRef.current) {
              setTimeout(() => {
                try {
                  chartRef.current?.timeScale().setVisibleRange(currentVisibleRange)
                  console.log('📊 [CustomTradingChart] Restored visible range after setData')
                } catch (error) {
                  console.warn('⚠️ [CustomTradingChart] Could not restore visible range:', error)
                }
              }, 50)
            }
          } else {
            // Use individual update() calls for smaller historical loads to prevent scroll glitches
            console.log(`📊 [CustomTradingChart] Using individual updates for ${newHistoricalCount} historical candles`)
            
            // Sort new historical data to update in chronological order
            const sortedHistoricalData = [...historicalData].sort((a, b) => {
              const timeA = typeof a.time === 'string' ? new Date(a.time).getTime() : a.time
              const timeB = typeof b.time === 'string' ? new Date(b.time).getTime() : b.time
              return timeA - timeB
            })
            
            // Update each historical candle individually
            for (const candle of sortedHistoricalData) {
              try {
                const formatted = formatSingleDataPoint(candle)
                seriesRef.current.update(formatted)
              } catch (error) {
                console.warn('⚠️ [CustomTradingChart] Failed to update historical candle:', error, candle)
              }
            }
            
            console.log(`📊 [CustomTradingChart] Successfully updated ${sortedHistoricalData.length} historical candles individually`)
          }
          
          console.log(`📊 [CustomTradingChart] Successfully merged ${historicalData.length} historical candles, total: ${uniqueData.length}`)
        }
      },
      getVisibleTimeRange: () => {
        if (chartRef.current) {
          const timeScale = chartRef.current.timeScale()
          const visibleRange = timeScale.getVisibleRange()
          if (visibleRange) {
            return {
              from: visibleRange.from as number,
              to: visibleRange.to as number
            }
          }
        }
        return null
      },
      getCurrentDataRange: () => {
        const currentData = allDataRef.current
        if (currentData.length === 0) return null
        
        const times = currentData.map(item => 
          typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
        )
        
        return {
          earliest: Math.min(...times),
          latest: Math.max(...times)
        }
      },
      scrollToRealtime: () => {
        if (chartRef.current) {
          try {
            // PHASE 1: Reset scroll state and enable auto-scroll
            isViewingHistoricalDataRef.current = false
            isUserScrollingRef.current = false
            autoScrollToLiveEnabledRef.current = true
            
            if (scrollTimeoutRef.current) {
              clearTimeout(scrollTimeoutRef.current)
              scrollTimeoutRef.current = null
            }
            
            // Use TradingView's recommended scrollToRealTime method for live charts
            chartRef.current.timeScale().scrollToRealTime()
            console.log('📊 [CustomTradingChart] Scrolled to real-time view and enabled auto-scroll')
          } catch (error) {
            console.error('❌ [CustomTradingChart] Error scrolling to real-time:', error)
          }
        }
      },
      
      // PHASE 1: New methods for scroll state management
      enableAutoScroll: () => {
        autoScrollToLiveEnabledRef.current = true
        isViewingHistoricalDataRef.current = false
        console.log('✅ [CustomTradingChart] Auto-scroll to live enabled')
      },
      
      disableAutoScroll: () => {
        autoScrollToLiveEnabledRef.current = false
        console.log('🚫 [CustomTradingChart] Auto-scroll to live disabled')
      },
      
      isViewingHistoricalData: () => {
        return isViewingHistoricalDataRef.current
      },
      
      getScrollState: () => {
        return {
          isViewingHistoricalData: isViewingHistoricalDataRef.current,
          isUserScrolling: isUserScrollingRef.current,
          autoScrollEnabled: autoScrollToLiveEnabledRef.current
        }
      }
    }))

    // Format OHLC data for candlestick charts with proper time ordering
    const formatData = (rawData: ChartData[]) => {
      if (!rawData || !Array.isArray(rawData)) {
        console.warn('📊 [CustomTradingChart] Invalid rawData:', rawData)
        return []
      }

      console.log('📊 [CustomTradingChart] Processing', rawData.length, 'raw data items')

      const processedData = rawData
        .filter(item => item && typeof item === 'object' && item.time !== undefined)
        .map(item => {
          // Ensure time is a valid timestamp
          let timeValue: number
          if (typeof item.time === 'string') {
            const parsed = new Date(item.time).getTime() / 1000
            if (isNaN(parsed)) {
              console.warn('📊 [CustomTradingChart] Invalid date string:', item.time)
              return null
            }
            timeValue = parsed
          } else if (typeof item.time === 'number') {
            if (!isFinite(item.time) || item.time <= 0) {
              console.warn('📊 [CustomTradingChart] Invalid timestamp:', item.time)
              return null
            }
            timeValue = item.time
          } else {
            console.warn('📊 [CustomTradingChart] Invalid time type:', typeof item.time, item.time)
            return null
          }

          // 🔍 ENHANCED OHLC VALIDATION - NO MOCK DATA ALLOWED
          const open = Number(item.open)
          const high = Number(item.high)
          const low = Number(item.low)
          const close = Number(item.close)

          // Validate all OHLC values are finite numbers
          if (!isFinite(open) || !isFinite(high) || !isFinite(low) || !isFinite(close)) {
            console.warn('📊 [CustomTradingChart] Non-finite OHLC values:', { open, high, low, close })
            return null
          }

          // Reject ALL zero values (invalid data)
          if (open === 0 && high === 0 && low === 0 && close === 0) {
            console.warn('📊 [CustomTradingChart] All zero OHLC values:', { open, high, low, close })
            return null
          }
          
          // ⚡ CRITICAL: Reject flat/identical OHLC values (indicates flat line data)
          if (open === high && high === low && low === close && open > 0) {
            console.warn('📊 [CustomTradingChart] FLAT CANDLESTICK DETECTED - All OHLC values identical:', { open, high, low, close })
            return null
          }
          
          // ✅ ENHANCED: Additional flat line detection patterns
          const ohlcValues = [open, high, low, close]
          const uniqueValues = new Set(ohlcValues)
          if (uniqueValues.size === 1 && open > 0) {
            console.warn('📊 [CustomTradingChart] FLAT LINE PATTERN - All OHLC values identical:', { open, high, low, close })
            return null
          }
          
          // ✅ ENHANCED: Detect near-flat patterns (within 0.0001% variance)
          const minVal = Math.min(...ohlcValues)
          const maxVal = Math.max(...ohlcValues)
          const variance = maxVal - minVal
          const avgVal = (maxVal + minVal) / 2
          const variancePercentage = avgVal > 0 ? (variance / avgVal) * 100 : 0
          
          if (variancePercentage < 0.0001 && avgVal > 0) {
            console.warn('📊 [CustomTradingChart] NEAR-FLAT PATTERN detected - Variance too small:', { 
              variance, variancePercentage, ohlc: { open, high, low, close } 
            })
            return null
          }
          
          // Validate proper OHLC relationships
          if (high < Math.max(open, close) || low > Math.min(open, close)) {
            console.warn('📊 [CustomTradingChart] Invalid OHLC relationships:', { open, high, low, close })
            return null
          }
          
          // ✅ ENHANCED: More sophisticated spread validation
          const spread = high - low
          const avgPrice = (high + low) / 2
          const spreadPercentage = avgPrice > 0 ? (spread / avgPrice) * 100 : 0
          
          // Reject micro-spreads that create visual flat lines
          if (spreadPercentage < 0.001) { // Less than 0.001% spread
            console.warn('📊 [CustomTradingChart] Spread too small for valid candlestick:', { spread, spreadPercentage, avgPrice })
            return null
          }
          
          // ✅ NEW: Detect unrealistic price movements (sanity check)
          if (avgPrice > 0) {
            const maxReasonableMove = avgPrice * 0.5 // 50% maximum move
            if (spread > maxReasonableMove) {
              console.warn('📊 [CustomTradingChart] Unrealistic price spread detected:', { 
                spread, maxReasonableMove, spreadPercentage, ohlc: { open, high, low, close } 
              })
              return null
            }
          }

          // Fix invalid high/low relationships to ensure valid candlestick
          const validHigh = Math.max(open, high, low, close)
          const validLow = Math.min(open, high, low, close)

          return {
            time: timeValue,
            open,
            high: validHigh,
            low: validLow,
            close
          } as CandlestickData
        })
        .filter(item => item !== null) as CandlestickData[]

      // CRITICAL: Sort data by time in ascending order (required by LightweightCharts)
      const sortedData = processedData.sort((a, b) => {
        const timeA = a.time as number
        const timeB = b.time as number
        return timeA - timeB
      })

      // Remove duplicates (keep the latest data for each timestamp)
      const deduplicatedData: CandlestickData[] = []
      const timeMap = new Map<number, CandlestickData>()
      
      sortedData.forEach(item => {
        const time = item.time as number
        if (!timeMap.has(time) || timeMap.get(time)!.time < item.time) {
          timeMap.set(time, item)
        }
      })

      // Convert back to array and ensure final sort
      const finalData = Array.from(timeMap.values()).sort((a, b) => {
        const timeA = a.time as number
        const timeB = b.time as number
        return timeA - timeB
      })

      console.log('📊 [CustomTradingChart] Formatted data:', {
        input: rawData.length,
        processed: processedData.length,
        deduplicated: finalData.length,
        timeRange: finalData.length > 0 ? {
          earliest: new Date((finalData[0].time as number) * 1000).toISOString(),
          latest: new Date((finalData[finalData.length - 1].time as number) * 1000).toISOString()
        } : 'No data'
      })

      // Validate time ordering (extra safety check)
      for (let i = 1; i < finalData.length; i++) {
        const prevTime = finalData[i - 1].time as number
        const currTime = finalData[i].time as number
        if (currTime <= prevTime) {
          console.error('📊 [CustomTradingChart] TIME ORDERING VIOLATION DETECTED:', {
            index: i,
            prevTime,
            currTime,
            prevDate: new Date(prevTime * 1000).toISOString(),
            currDate: new Date(currTime * 1000).toISOString()
          })
          // Remove the offending item
          finalData.splice(i, 1)
          i-- // Adjust index after removal
        }
      }

      return finalData
    }

    const formatSingleDataPoint = (dataPoint: ChartData) => {
      if (!dataPoint || typeof dataPoint !== 'object') {
        console.warn('📊 [CustomTradingChart] Invalid dataPoint:', dataPoint)
        return null
      }

      // Ensure time is a valid timestamp
      let timeValue: number
      if (typeof dataPoint.time === 'string') {
        const parsed = new Date(dataPoint.time).getTime() / 1000
        if (isNaN(parsed)) {
          console.warn('📊 [CustomTradingChart] Invalid date string in dataPoint:', dataPoint.time)
          return null
        }
        timeValue = parsed
      } else if (typeof dataPoint.time === 'number') {
        if (!isFinite(dataPoint.time) || dataPoint.time <= 0) {
          console.warn('📊 [CustomTradingChart] Invalid timestamp in dataPoint:', dataPoint.time)
          return null
        }
        timeValue = dataPoint.time
      } else {
        console.warn('📊 [CustomTradingChart] Invalid time type in dataPoint:', typeof dataPoint.time, dataPoint.time)
        return null
      }

      // Enhanced OHLC validation for single data point
      const open = Number(dataPoint.open)
      const high = Number(dataPoint.high)
      const low = Number(dataPoint.low)
      const close = Number(dataPoint.close)

      // Validate all OHLC values are finite numbers
      if (!isFinite(open) || !isFinite(high) || !isFinite(low) || !isFinite(close)) {
        console.warn('📊 [CustomTradingChart] Non-finite OHLC values in dataPoint:', { open, high, low, close })
        return null
      }

      // Only reject if ALL values are zero (which indicates invalid data)
      if (open === 0 && high === 0 && low === 0 && close === 0) {
        console.warn('📊 [CustomTradingChart] All zero OHLC values in dataPoint:', { open, high, low, close })
        return null
      }

      // Fix invalid high/low relationships
      const validHigh = Math.max(open, high, low, close)
      const validLow = Math.min(open, high, low, close)

      return {
        time: timeValue,
        open,
        high: validHigh,
        low: validLow,
        close
      } as CandlestickData
    }

    // Helper function to convert timeframe string to seconds
    const getTimeframeSeconds = (timeframe: string): number => {
      const timeframeMap: { [key: string]: number } = {
        '1M': 60,
        '5M': 300,
        '15M': 900,
        '1H': 3600,
        '4H': 14400,
        '1D': 86400
      }
      return timeframeMap[timeframe] || 3600 // Default to 1 hour
    }

    // Manual history load function
    const handleManualLoadHistory = async () => {
      if (!onLoadMoreHistory || isLoadingHistoryRef.current) return
      
      const currentDataRange = allDataRef.current.length > 0 ? {
        earliest: Math.min(...allDataRef.current.map(item => 
          typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
        )),
        latest: Math.max(...allDataRef.current.map(item => 
          typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
        ))
      } : null
      
      if (!currentDataRange) return
      
      const dataSpan = currentDataRange.latest - currentDataRange.earliest
      const timeframeSeconds = getTimeframeSeconds(interval)
      const requestedTimeSpan = Math.max(dataSpan, timeframeSeconds * 100, 3600)
      
      const historyEndTime = currentDataRange.earliest - (timeframeSeconds * 2)
      const historyStartTime = historyEndTime - requestedTimeSpan
      
      console.log('📊 [CustomTradingChart] Manual history load requested')
      isLoadingHistoryRef.current = true
      setShowManualLoad(false)
      
      try {
        await onLoadMoreHistory(historyStartTime, historyEndTime, allDataRef.current.length)
        console.log('✅ [CustomTradingChart] Manual history load completed')
      } catch (error) {
        console.error('❌ [CustomTradingChart] Manual history load failed:', error)
      } finally {
        isLoadingHistoryRef.current = false
      }
    }


    useEffect(() => {
      if (!chartContainerRef.current) {
        console.warn('📊 [CustomTradingChart] Chart container ref not available')
        return () => {} // Empty cleanup function for early return
      }

      console.log('📊 [CustomTradingChart] Initializing chart for', symbol, interval)

      // ✅ FRONTEND INDEPENDENCE: Reset states
      setChartReady(false)
      setChartError(null)

      // ✅ FRONTEND INDEPENDENCE: Create chart with error boundary
      let chart: IChartApi | null = null
      try {
        chart = createChart(chartContainerRef.current, {
        layout: {
          background: { type: ColorType.Solid, color: 'transparent' },
          textColor: '#D1D5DB', // text-gray-300
        },
        width: chartContainerRef.current.clientWidth,
        height,
        grid: {
          vertLines: {
            color: '#374151', // gray-700
          },
          horzLines: {
            color: '#374151', // gray-700
          },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
        },
        rightPriceScale: {
          borderColor: '#4B5563', // gray-600
        },
        timeScale: {
          borderColor: '#4B5563', // gray-600
          timeVisible: true,
          secondsVisible: false,
          rightOffset: 20, // Add space on the right for future price line
        },
      })

      if (!chart) {
        console.error('📊 [CustomTradingChart] Failed to create chart instance')
        return () => {}
      }

      chartRef.current = chart
      console.log('✅ [CustomTradingChart] Chart instance created successfully')

      // ✅ FRONTEND INDEPENDENCE: Create candlestick series with error boundary
      let series: any = null
      try {
        series = chart.addSeries(CandlestickSeries, {
        upColor: '#10B981', // green-500
        downColor: '#EF4444', // red-500
        borderVisible: false,
        wickUpColor: '#10B981',
        wickDownColor: '#EF4444',
        priceFormat: {
          type: 'price',
          precision: chartDecimals,
          minMove: Math.pow(10, -chartDecimals)
        }
      })

      if (!series) {
        console.error('📊 [CustomTradingChart] Failed to create candlestick series')
        chart.remove()
        return () => {}
      }

      seriesRef.current = series
      console.log('✅ [CustomTradingChart] Candlestick series created successfully')

      // ✅ FRONTEND INDEPENDENCE: Set initial data with error handling
      if (data && data.length > 0) {
        try {
          const formattedData = formatData(data)
          if (formattedData.length > 0) {
            series.setData(formattedData)
            console.log('✅ [CustomTradingChart] Initial data loaded:', formattedData.length, 'candles')
          } else {
            console.warn('📊 [CustomTradingChart] No valid formatted data for initial load')
          }
        } catch (error) {
          console.error('❌ [CustomTradingChart] Error setting initial data:', error)
          // Set empty data to show chart without errors
          series.setData([])
        }
      } else {
        console.log('📊 [CustomTradingChart] No initial data provided - chart ready for updates')
        // ✅ FRONTEND INDEPENDENCE: Show empty chart that's ready for data
        series.setData([])
      }
      
      // ✅ FRONTEND INDEPENDENCE: Mark chart as ready
      setChartReady(true)
      console.log('✅ [CustomTradingChart] Chart fully initialized and ready')
      
      // Handle crosshair move
      if (onCrosshairMove) {
        chart.subscribeCrosshairMove(onCrosshairMove)
      }

      // Subscribe to time scale changes to detect zoom and pan
      const timeScale = chart.timeScale()
      
      const handleVisibleTimeRangeChange = () => {
        // PHASE 1: Detect user-initiated scroll vs automatic updates
        const now = Date.now()
        const visibleRange = timeScale.getVisibleRange()
        const currentDataCount = allDataRef.current.length
        
        // PHASE 1: User scroll state tracking
        if (visibleRange) {
          const visibleFrom = visibleRange.from as number
          const visibleTo = visibleRange.to as number
          
          // Check if this is a user-initiated scroll by comparing with data range
          const currentDataRange = currentDataCount > 0 ? {
            earliest: Math.min(...allDataRef.current.map(item => 
              typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
            )),
            latest: Math.max(...allDataRef.current.map(item => 
              typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
            ))
          } : null
          
          if (currentDataRange) {
            // PHASE 1: Detect if user is viewing historical data (not at live edge)
            const timeFromLiveEdge = currentDataRange.latest - visibleTo
            const isNearLiveEdge = timeFromLiveEdge < getTimeframeSeconds(interval) * 2 // Within 2 candle periods
            const wasViewingHistoricalData = isViewingHistoricalDataRef.current
            
            isViewingHistoricalDataRef.current = !isNearLiveEdge
            
            // PHASE 1: Update UI indicators based on scroll state
            setShowHistoricalIndicator(!isNearLiveEdge)
            setShowJumpToLiveButton(!isNearLiveEdge)
            
            // PHASE 1: Detect user scrolling behavior
            if (visibleFrom < lastVisibleFromRef.current) {
              // User scrolling backward (into history)
              if (!isUserScrollingRef.current) {
                console.log('🔙 [CustomTradingChart] User started scrolling backward into history')
                userScrollStartTimeRef.current = now
              }
              
              isUserScrollingRef.current = true
              autoScrollToLiveEnabledRef.current = false
              
              // Reset scroll timeout
              if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current)
              }
              
              // Set timeout to detect end of scrolling
              scrollTimeoutRef.current = setTimeout(() => {
                isUserScrollingRef.current = false
                console.log('✋ [CustomTradingChart] User stopped scrolling - will preserve position')
              }, 1000) // 1 second timeout
              
            } else if (isNearLiveEdge && wasViewingHistoricalData) {
              // User scrolled back to live edge
              console.log('⚡ [CustomTradingChart] User returned to live edge - enabling auto-scroll')
              autoScrollToLiveEnabledRef.current = true
              isUserScrollingRef.current = false
              
              // PHASE 1: Hide UI indicators when returning to live
              setShowHistoricalIndicator(false)
              setShowJumpToLiveButton(false)
              
              if (scrollTimeoutRef.current) {
                clearTimeout(scrollTimeoutRef.current)
                scrollTimeoutRef.current = null
              }
            }
            
            console.log('📊 [CustomTradingChart] Scroll state:', {
              isViewingHistoricalData: isViewingHistoricalDataRef.current,
              isUserScrolling: isUserScrollingRef.current,
              autoScrollEnabled: autoScrollToLiveEnabledRef.current,
              timeFromLiveEdge: `${timeFromLiveEdge}s`,
              isNearLiveEdge,
              scrollDirection: visibleFrom < lastVisibleFromRef.current ? '🔙' : '🔜'
            })
          }
        }
        
        console.log('📊 [CustomTradingChart] Time range change detected:', {
          isLoadingHistory: isLoadingHistoryRef.current,
          currentDataCount,
          visibleRange: visibleRange ? {
            from: new Date((visibleRange.from as number) * 1000),
            to: new Date((visibleRange.to as number) * 1000)
          } : 'null'
        })
        
        if (isLoadingHistoryRef.current) {
          console.log('📊 [CustomTradingChart] Already loading history, skipping...')
          return () => {}
        }
        
        // PHASE 1: Aggressive cooldown for infinite scrolling
        const currentTime = Date.now()
        const cooldownPeriod = isScrollingBackwardRef.current ? 100 : 200 // PHASE 1: Much shorter cooldowns for responsive loading
        if (now - lastHistoryRequestRef.current < cooldownPeriod) {
          console.log(`📊 [CustomTradingChart] Request cooldown active (${cooldownPeriod}ms), skipping...`)
          return () => {}
        }
        
        const currentDataRange = currentDataCount > 0 ? {
          earliest: Math.min(...allDataRef.current.map(item => 
            typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
          )),
          latest: Math.max(...allDataRef.current.map(item => 
            typeof item.time === 'string' ? new Date(item.time).getTime() / 1000 : item.time
          ))
        } : null
        
        if (visibleRange && currentDataRange && onLoadMoreHistory) {
          const visibleFrom = visibleRange.from as number
          const visibleTo = visibleRange.to as number
          
          // Detect backward scrolling for progressive loading
          const previousVisibleFrom = lastVisibleFromRef.current
          const isMovingBackward = visibleFrom < previousVisibleFrom
          isScrollingBackwardRef.current = isMovingBackward
          lastVisibleFromRef.current = visibleFrom
          
          console.log('📊 [CustomTradingChart] Range analysis:', {
            visibleFrom: new Date(visibleFrom * 1000),
            visibleTo: new Date(visibleTo * 1000),
            dataEarliest: new Date(currentDataRange.earliest * 1000),
            dataLatest: new Date(currentDataRange.latest * 1000),
            currentDataCount,
            isMovingBackward,
            scrollDirection: isMovingBackward ? '←' : '→'
          })
          
          // Notify parent about time range change
          if (onTimeRangeChange) {
            onTimeRangeChange(visibleFrom, visibleTo)
          }
          
          // OPTIMIZED trigger logic: More conservative to prevent zoom glitches
          const dataSpan = currentDataRange.latest - currentDataRange.earliest
          const timeframeSeconds = getTimeframeSeconds(interval)
          const visibleSpan = visibleTo - visibleFrom
          
          // PHASE 1: Aggressive triggers for infinite scrolling
          const dataGap = Math.max(0, currentDataRange.earliest - visibleFrom)
          const significantGapThreshold = timeframeSeconds * 3 // PHASE 1: Reduced from 20 to 3 candles
          const needsHistoricalData = dataGap > significantGapThreshold
          
          // PHASE 1: Much more aggressive boundary detection
          const scrollBackwardMultiplier = isMovingBackward ? 2.0 : 1.0
          const boundaryDistance = timeframeSeconds * (5 * scrollBackwardMultiplier) // PHASE 1: Reduced from 20x to 5x
          const approachingBoundary = isMovingBackward && visibleFrom <= (currentDataRange.earliest + boundaryDistance)
          
          // PHASE 1: Aggressive gap detection - load with minimal gaps
          const backwardScrollingGap = isMovingBackward && dataGap > (timeframeSeconds * 2) // PHASE 1: Reduced from 10 to 2 candles
          
          // PHASE 1: Aggressive loading for unlimited historical access
          const hasMinimumData = currentDataCount >= 5 // PHASE 1: Even lower threshold for immediate loading
          // PHASE 2: Add proactive edge detection and proximity loading
          const visibleDataPercentage = Math.abs(visibleFrom - currentDataRange.earliest) / (currentDataRange.latest - currentDataRange.earliest)
          const proximityToEdge = visibleDataPercentage < 0.2 // PHASE 2: Load when 80% of visible area shows existing data
          const viewportBasedTrigger = (visibleFrom - currentDataRange.earliest) < (visibleSpan * 0.3) // PHASE 2: Load when within 30% of viewport from edge
          
          // PHASE 2: Momentum-based prediction
          const scrollVelocity = Math.abs(visibleFrom - lastVisibleFromRef.current)
          const highScrollVelocity = scrollVelocity > (timeframeSeconds * 5) // Fast scrolling detected
          const momentumPreloading = isMovingBackward && highScrollVelocity
          
          // PHASE 1: Use OR logic instead of AND - load if ANY condition is met
          const shouldLoad = hasMinimumData && (
            needsHistoricalData || 
            approachingBoundary || 
            backwardScrollingGap ||
            proximityToEdge ||        // PHASE 2: New proactive trigger
            viewportBasedTrigger ||   // PHASE 2: New viewport trigger  
            momentumPreloading        // PHASE 2: New momentum trigger
          )
          
          console.log('📊 [CustomTradingChart] PHASE 2 - Proactive trigger conditions:', {
            needsHistoricalData,
            approachingBoundary,
            backwardScrollingGap,
            proximityToEdge,           // PHASE 2: New trigger
            viewportBasedTrigger,      // PHASE 2: New trigger
            momentumPreloading,        // PHASE 2: New trigger
            shouldLoad,
            hasMinimumData,
            currentDataCount,
            isMovingBackward,
            scrollVelocity: `${scrollVelocity}s`,
            visibleDataPercentage: `${(visibleDataPercentage * 100).toFixed(1)}%`,
            scrollBackwardMultiplier,
            boundaryDistance: `${boundaryDistance}s`,
            visibleFromVsEarliest: `${visibleFrom - currentDataRange.earliest}s`,
            dataGap: `${dataGap}s (${Math.ceil(dataGap / timeframeSeconds)} candles)`,
            significantGapThreshold: `${significantGapThreshold}s`,
            visibleSpan: `${visibleSpan}s`,
            dataSpan: `${dataSpan}s`,
            timeframeSeconds: `${timeframeSeconds}s`
          })
          
          // PHASE 1: AGGRESSIVE loading: Load when any condition is met
          const shouldLoadMore = shouldLoad
          
          if (shouldLoadMore) {
            console.log('🚀 [CustomTradingChart] TRIGGERING historical data load!')
            console.log('📊 [CustomTradingChart] Visible range:', { 
              from: new Date(visibleFrom * 1000), 
              to: new Date(visibleTo * 1000) 
            })
            console.log('📊 [CustomTradingChart] Current data range:', { 
              earliest: new Date(currentDataRange.earliest * 1000), 
              latest: new Date(currentDataRange.latest * 1000) 
            })
            
            isLoadingHistoryRef.current = true
            lastHistoryRequestRef.current = now
            
            // Generate unique request ID to prevent race conditions
            const requestId = `${now}_${Math.random().toString(36).substr(2, 9)}`
            pendingHistoryRequestRef.current = requestId
            
            // PHASE 2: Unlimited historical data loading with dynamic ranges
            const gapToCover = Math.max(currentDataRange.earliest - visibleFrom, 0)
            const visibleTimeSpan = visibleSpan // How much time user is currently viewing
            
            // PHASE 3: Continuous background loading with progressive buffering
            const backwardScrollMultiplier = isScrollingBackwardRef.current ? 15.0 : 8.0 // PHASE 3: Even more aggressive for seamless experience
            
            // PHASE 3: Maintain 2-3x visible range buffer at all times
            const bufferMultiplier = proximityToEdge ? 5.0 : 3.0 // Larger buffer when approaching edge
            const continuousBuffer = visibleTimeSpan * bufferMultiplier
            const extraBuffer = Math.max(continuousBuffer, dataSpan * 2)
            
            // PHASE 3: Progressive chunk sizing based on scroll behavior
            const baseChunkSize = timeframeSeconds * 1000 // Base 1000 candles
            const velocityMultiplier = highScrollVelocity ? 3.0 : 1.0 // Larger chunks for fast scrolling
            const adaptiveChunkSize = baseChunkSize * velocityMultiplier * backwardScrollMultiplier
            
            // PHASE 3: Seamless infinite loading calculation
            const requestedTimeSpan = Math.max(
              gapToCover + extraBuffer, // Cover gap + continuous buffer
              visibleTimeSpan * (15 * backwardScrollMultiplier), // Up to 225x visible when scrolling back
              adaptiveChunkSize, // Adaptive chunk size based on behavior
              86400 * 60 * backwardScrollMultiplier // Up to 900+ days when scrolling back aggressively
            )
            
            const historyEndTime = currentDataRange.earliest - (timeframeSeconds * 2) // Leave 2 candle periods gap
            const historyStartTime = historyEndTime - requestedTimeSpan
            
            console.log('📊 [CustomTradingChart] PHASE 3 - Continuous background loading:', {
              startTime: new Date(historyStartTime * 1000),
              endTime: new Date(historyEndTime * 1000),
              timeSpan: `${requestedTimeSpan}s`,
              estimatedCandles: Math.ceil(requestedTimeSpan / timeframeSeconds),
              currentDataLength: allDataRef.current.length,
              bufferMultiplier,
              velocityMultiplier,
              adaptiveChunkSize: Math.ceil(adaptiveChunkSize / timeframeSeconds),
              triggerReason: {
                proximityToEdge,
                viewportBasedTrigger,
                momentumPreloading,
                needsHistoricalData,
                approachingBoundary,
                backwardScrollingGap
              }
            })
            
            onLoadMoreHistory(historyStartTime, historyEndTime, allDataRef.current.length)
              .then((historicalData) => {
                // Check if this is still the current request (prevent race conditions)
                if (pendingHistoryRequestRef.current !== requestId) {
                  console.log('📊 [CustomTradingChart] Ignoring outdated historical data response')
                  return () => {}
                }
                
                if (historicalData && historicalData.length > 0) {
                  console.log(`✅ [CustomTradingChart] Successfully received ${historicalData.length} historical candles`)
                } else {
                  console.warn('⚠️ [CustomTradingChart] No historical data received')
                }
              })
              .catch((error) => {
                // Check if this is still the current request
                if (pendingHistoryRequestRef.current !== requestId) {
                  console.log('📊 [CustomTradingChart] Ignoring outdated historical data error')
                  return () => {}
                }
                console.error('❌ [CustomTradingChart] Failed to load historical data:', error)
              })
              .finally(() => {
                // Only clear loading state if this is still the current request
                if (pendingHistoryRequestRef.current === requestId) {
                  isLoadingHistoryRef.current = false
                  pendingHistoryRequestRef.current = null
                  console.log('📊 [CustomTradingChart] Historical data loading completed')
                }
              })
          } else {
            console.log('📊 [CustomTradingChart] No need to load historical data yet')
            
            // Show manual load button if user is viewing area that could benefit from historical data
            if (showManualLoadButton && onLoadMoreHistory && 
                visibleFrom < currentDataRange.earliest && 
                currentDataCount >= 10 && 
                !showManualLoadRef.current) {
              console.log('📊 [CustomTradingChart] Showing manual load history button')
              showManualLoadRef.current = true
              setShowManualLoad(true)
            }
          }
        } else {
          console.log('📊 [CustomTradingChart] Missing requirements for historical data loading:', {
            hasVisibleRange: !!visibleRange,
            hasCurrentDataRange: !!currentDataRange,
            hasLoadCallback: !!onLoadMoreHistory
          })
        }
      }
      
      // Minimal debounce for immediate responsiveness with real data
      let timeRangeChangeTimeout: NodeJS.Timeout | null = null
      const debouncedTimeRangeChange = () => {
        if (timeRangeChangeTimeout) {
          clearTimeout(timeRangeChangeTimeout)
        }
        timeRangeChangeTimeout = setTimeout(handleVisibleTimeRangeChange, 50) // Reduced to 50ms for immediate response
      }
      
      // Also trigger immediately on zoom out for better UX
      const immediateTimeRangeChange = () => {
        // Clear any pending debounced call
        if (timeRangeChangeTimeout) {
          clearTimeout(timeRangeChangeTimeout)
          timeRangeChangeTimeout = null
        }
        handleVisibleTimeRangeChange()
      }
      
      timeScale.subscribeVisibleTimeRangeChange(debouncedTimeRangeChange)
      
      // Add mouse wheel listener for immediate zoom detection
      const handleWheel = (event: WheelEvent) => {
        if (event.ctrlKey || event.metaKey) {
          // Zoom action detected, trigger immediate check
          setTimeout(immediateTimeRangeChange, 50)
        }
      }
      
      chartContainerRef.current?.addEventListener('wheel', handleWheel)

      // INTERACTION FIX: Add interaction detection to pause updates during user actions
      const handleUserInteractionStart = () => {
        isUserInteractingRef.current = true
        if (interactionTimeoutRef.current) {
          clearTimeout(interactionTimeoutRef.current)
        }
        console.log('🖱️ [CustomTradingChart] User interaction started - pausing updates')
      }

      const handleUserInteractionEnd = () => {
        // Debounce interaction end to avoid rapid on/off switching
        if (interactionTimeoutRef.current) {
          clearTimeout(interactionTimeoutRef.current)
        }
        interactionTimeoutRef.current = setTimeout(() => {
          isUserInteractingRef.current = false
          console.log('🖱️ [CustomTradingChart] User interaction ended - resuming updates')
        }, 500) // 500ms delay after last interaction
      }

      // Add interaction event listeners
      const chartContainer = chartContainerRef.current
      if (chartContainer) {
        // Mouse events
        chartContainer.addEventListener('mousedown', handleUserInteractionStart)
        chartContainer.addEventListener('mousemove', handleUserInteractionStart)
        chartContainer.addEventListener('mouseup', handleUserInteractionEnd)
        chartContainer.addEventListener('wheel', handleUserInteractionStart)
        
        // Touch events for mobile
        chartContainer.addEventListener('touchstart', handleUserInteractionStart)
        chartContainer.addEventListener('touchmove', handleUserInteractionStart)
        chartContainer.addEventListener('touchend', handleUserInteractionEnd)
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current) {
          chart.applyOptions({
            width: chartContainerRef.current.clientWidth,
          })
        }
      }

      window.addEventListener('resize', handleResize)

      } catch (chartError) {
      const errorMessage = chartError instanceof Error ? chartError.message : 'Unknown chart initialization error'
      console.error('❌ [CustomTradingChart] Critical error during chart initialization:', chartError)
      
      // ✅ FRONTEND INDEPENDENCE: Set error state
      setChartError(errorMessage)
      setChartReady(false)
      
      // Clean up any partial initialization
      if (chart) {
        try {
          chart.remove()
        } catch (cleanupError) {
          console.error('❌ [CustomTradingChart] Error during cleanup:', cleanupError)
        }
      }
    }

    // Cleanup function
    return () => {
        window.removeEventListener('resize', handleResize)
        chartContainerRef.current?.removeEventListener('wheel', handleWheel)
        
        // Clean up interaction event listeners
        if (chartContainer) {
          chartContainer.removeEventListener('mousedown', handleUserInteractionStart)
          chartContainer.removeEventListener('mousemove', handleUserInteractionStart)
          chartContainer.removeEventListener('mouseup', handleUserInteractionEnd)
          chartContainer.removeEventListener('wheel', handleUserInteractionStart)
          chartContainer.removeEventListener('touchstart', handleUserInteractionStart)
          chartContainer.removeEventListener('touchmove', handleUserInteractionStart)
          chartContainer.removeEventListener('touchend', handleUserInteractionEnd)
        }
        
        // Clean up timeouts
        if (interactionTimeoutRef.current) {
          clearTimeout(interactionTimeoutRef.current)
        }
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current)
        }
        if (timeRangeChangeTimeout) {
          clearTimeout(timeRangeChangeTimeout)
        }
        
        // ✅ FIXED: Clean up chart subscriptions properly
        if (chart && onCrosshairMove) {
          try {
            chart.unsubscribeCrosshairMove(onCrosshairMove)
          } catch (error) {
            console.warn('⚠️ [CustomTradingChart] Error unsubscribing crosshair:', error)
          }
        }
        
        // Clean up time scale subscription
        if (chart) {
          try {
            const timeScale = chart.timeScale()
            timeScale.unsubscribeVisibleTimeRangeChange(debouncedTimeRangeChange)
          } catch (error) {
            console.warn('⚠️ [CustomTradingChart] Error unsubscribing time scale:', error)
          }
        }
        
        // ✅ ENHANCED: Remove chart with error handling
        if (chart) {
          try {
            chart.remove()
            console.log('✅ [CustomTradingChart] Chart removed successfully')
          } catch (error) {
            console.error('❌ [CustomTradingChart] Error removing chart:', error)
          }
        }
        
        // Always reset refs to null
        chartRef.current = null
        seriesRef.current = null
        allDataRef.current = []
        console.log('✅ [CustomTradingChart] Cleanup completed for', symbol)
      }
    }
    }, [symbol, interval, height, chartDecimals]) // Re-create chart when symbol, interval, height, or decimals change

    // HYBRID APPROACH: Initial data via props, updates via methods
    useEffect(() => {
      console.log('📊 [CustomTradingChart] Data changed:', data?.length || 0, 'points for symbol:', symbol)
      
      if (seriesRef.current && data && data.length > 0) {
        // Update internal data reference for historical context
        allDataRef.current = [...data]
        
        // CRITICAL: Load initial data via setData for display
        // This ensures the chart shows historical data on load
        const formattedData = formatData(data)
        console.log('📊 [CustomTradingChart] Loading initial chart data:')
        console.log('  📊 Raw data length:', data.length)
        console.log('  📊 Formatted data length:', formattedData.length)
        console.log('  📊 First 3 raw items:', data.slice(0, 3))
        console.log('  📊 First 3 formatted items:', formattedData.slice(0, 3))
        
        if (formattedData.length > 0) {
          // Final validation before setData
          let isValid = true
          for (let i = 1; i < formattedData.length; i++) {
            const prevTime = formattedData[i - 1].time as number
            const currTime = formattedData[i].time as number
            if (currTime <= prevTime) {
              console.error('📊 [CustomTradingChart] CRITICAL: Time ordering violation in initial data!', {
                index: i,
                prevTime,
                currTime,
                prevDate: new Date(prevTime * 1000).toISOString(),
                currDate: new Date(currTime * 1000).toISOString()
              })
              isValid = false
              break
            }
          }
          
          if (isValid) {
            try {
              seriesRef.current.setData(formattedData)
              console.log('✅ [CustomTradingChart] Successfully loaded initial data')
            } catch (error) {
              console.error('❌ [CustomTradingChart] Error in initial setData:', error)
              // Clear chart on error
              seriesRef.current.setData([])
            }
          } else {
            console.error('📊 [CustomTradingChart] Skipping initial data load due to time ordering issues')
            seriesRef.current.setData([])
          }
        } else {
          console.warn('📊 [CustomTradingChart] No valid data after formatting, clearing chart')
          seriesRef.current.setData([])
        }
        
        // Fit content after loading initial data
        setTimeout(() => {
          if (chartRef.current) {
            chartRef.current.timeScale().fitContent()
          }
        }, 100)
        
      } else if (!data || data.length === 0) {
        // Clear chart when no data
        console.log('📊 [CustomTradingChart] Clearing chart data for:', symbol)
        allDataRef.current = []
        if (seriesRef.current) {
          seriesRef.current.setData([])
        }
      }
    }, [data, symbol]) // Watch both data and symbol changes

    return (
      <div className="relative w-full">
        {/* ✅ FRONTEND INDEPENDENCE: Chart Error State */}
        {chartError && (
          <div className="absolute inset-0 z-30 flex items-center justify-center bg-gray-900/80 backdrop-blur-sm rounded-lg">
            <div className="text-center p-6">
              <div className="text-red-400 text-lg font-semibold mb-2">Chart Error</div>
              <div className="text-red-300 text-sm mb-4">{chartError}</div>
              <button
                onClick={() => {
                  setChartError(null)
                  setChartReady(false)
                  // Force re-initialization by updating a key dependency
                }}
                className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-lg text-red-400 text-sm transition-colors"
              >
                Retry Chart
              </button>
            </div>
          </div>
        )}

        {/* ✅ FRONTEND INDEPENDENCE: Chart Loading State */}
        {!chartReady && !chartError && (
          <div className="absolute inset-0 z-20 flex items-center justify-center bg-gray-900/60 backdrop-blur-sm rounded-lg">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-3"></div>
              <div className="text-blue-400 text-sm font-medium">Initializing Chart...</div>
              <div className="text-gray-400 text-xs mt-1">{symbol} • {interval}</div>
            </div>
          </div>
        )}

        {/* PHASE 4: Enhanced Historical Data Loading Indicator */}
        {isLoadingHistory && chartReady && (
          <div className="absolute top-2 right-2 z-20 flex items-center space-x-2 px-3 py-2 bg-blue-500/20 border border-blue-500/30 rounded-lg backdrop-blur-sm">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
            <span className="text-blue-400 text-xs font-medium">
              Loading more history...
            </span>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          </div>
        )}

        {/* PHASE 1: Historical Data Viewing Indicator */}
        {showHistoricalIndicator && (
          <div className="absolute top-2 left-2 z-20 flex items-center space-x-2 px-3 py-2 bg-amber-500/20 border border-amber-500/30 rounded-lg backdrop-blur-sm">
            <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
            <span className="text-amber-400 text-xs font-medium">
              📜 Viewing Historical Data
            </span>
          </div>
        )}

        {/* PHASE 1: Jump to Live Button */}
        {showJumpToLiveButton && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-20">
            <button
              onClick={() => {
                // Reset scroll state and jump to live
                if (chartRef.current) {
                  const chartMethods = {
                    scrollToRealtime: () => {
                      isViewingHistoricalDataRef.current = false
                      isUserScrollingRef.current = false
                      autoScrollToLiveEnabledRef.current = true
                      setShowHistoricalIndicator(false)
                      setShowJumpToLiveButton(false)
                      
                      if (scrollTimeoutRef.current) {
                        clearTimeout(scrollTimeoutRef.current)
                        scrollTimeoutRef.current = null
                      }
                      
                      try {
                        chartRef.current?.timeScale().scrollToRealTime()
                        console.log('🚀 [CustomTradingChart] Jumped to live view via button')
                      } catch (error) {
                        console.error('❌ [CustomTradingChart] Error jumping to live:', error)
                      }
                    }
                  }
                  chartMethods.scrollToRealtime()
                }
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-green-500/20 border border-green-500/30 rounded-lg backdrop-blur-sm hover:bg-green-500/30 transition-colors"
            >
              <span className="text-green-400 text-sm font-medium">⚡ Jump to Live</span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </button>
          </div>
        )}
        
        {/* Manual Load History Button */}
        {showManualLoad && !isLoadingHistory && showManualLoadButton && (
          <div className="absolute top-2 right-2 z-20">
            <button
              onClick={handleManualLoadHistory}
              className="flex items-center space-x-2 px-3 py-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 rounded-lg transition-colors cursor-pointer"
              title="Load more historical data"
            >
              <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="text-green-400 text-xs font-medium">Load History</span>
            </button>
          </div>
        )}
        
        {/* Chart Header */}
        <div className="absolute top-2 left-2 z-10 flex items-center space-x-2">
          <div className="px-2 py-1 bg-black/50 rounded text-sm font-medium text-white">
            {symbol}
          </div>
          <div className="px-2 py-1 bg-black/50 rounded text-xs text-gray-300">
            {interval}
          </div>
          {allDataRef.current.length > 0 && (
            <div className="px-2 py-1 bg-black/50 rounded text-xs text-gray-400">
              {allDataRef.current.length.toLocaleString()} candles {/* PHASE 4: Format large numbers */}
            </div>
          )}
          {/* PHASE 4: Show infinite scroll indicator */}
          {allDataRef.current.length > 1000 && (
            <div className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-400">
              ∞ Infinite Scroll
            </div>
          )}
        </div>

        {/* Chart Container */}
        <div 
          ref={chartContainerRef} 
          className="w-full rounded-lg border border-gray-700/50"
          style={{ height }}
        />

        {/* Chart Footer */}
        <div className="absolute bottom-2 right-2 z-10">
          <div className="px-2 py-1 bg-black/50 rounded text-xs text-gray-400">
            Powered by Lightweight Charts
          </div>
        </div>
      </div>
    )
  }
)

CustomTradingChart.displayName = 'CustomTradingChart'

// ✅ ENHANCED: Wrapped chart component with error boundary
const TradingChartWithErrorBoundary = forwardRef<ChartMethods, CustomTradingChartProps>(
  (props, ref) => (
    <ChartErrorBoundary
      onError={(error, errorInfo) => {
        console.error('📊 [TradingChart] Error boundary caught:', error, errorInfo)
      }}
    >
      <CustomTradingChart {...props} ref={ref} />
    </ChartErrorBoundary>
  )
)

TradingChartWithErrorBoundary.displayName = 'TradingChartWithErrorBoundary'

export { 
  CustomTradingChart,
  TradingChartWithErrorBoundary as TradingChart,
  ChartErrorBoundary 
}