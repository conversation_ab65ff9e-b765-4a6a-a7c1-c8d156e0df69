/**
 * Automated Payout Service
 * Handles profit calculations and payout processing for funded accounts
 */

import { supabase } from '@/lib/supabase/client'
import type { TradingAccount, Payout, PayoutInsert } from '@/types/database'

interface PayoutCalculation {
  accountId: string
  totalProfit: number
  payoutAmount: number
  payoutPercentage: number
  profitPeriodStart: Date
  profitPeriodEnd: Date
  nextPayoutDate: Date
  eligibleForPayout: boolean
  reason?: string
}

interface PayoutSummary {
  totalAccounts: number
  eligibleAccounts: number
  totalPayoutAmount: number
  totalProfitGenerated: number
  averagePayoutAmount: number
  payoutsByFrequency: {
    [key: number]: {
      accounts: number
      totalPayout: number
    }
  }
}

export class PayoutService {
  private readonly DEFAULT_PAYOUT_PERCENTAGE = 14.0 // 14% to traders, 86% retained
  private readonly MIN_PAYOUT_AMOUNT = 10.0 // Minimum $10 payout threshold
  private readonly VALID_FREQUENCIES = [10, 14, 21] // Valid payout frequencies in days

  /**
   * Calculate pending payouts for all eligible accounts
   */
  async calculatePendingPayouts(): Promise<PayoutCalculation[]> {
    try {
      // Get all funded accounts that are due for payout
      const { data: accounts, error } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('phase', 'funded')
        .eq('status', 'active')
        .not('next_payout_date', 'is', null)
        .lte('next_payout_date', new Date().toISOString())

      if (error) {
        throw new Error('Failed to fetch accounts for payout calculation')
      }

      const calculations: PayoutCalculation[] = []

      for (const account of accounts || []) {
        const calculation = await this.calculateAccountPayout(account)
        calculations.push(calculation)
      }

      return calculations
    } catch (error) {
      console.error('Error calculating pending payouts:', error)
      throw error
    }
  }

  /**
   * Calculate payout for a specific account
   */
  async calculateAccountPayout(account: TradingAccount): Promise<PayoutCalculation> {
    try {
      const now = new Date()
      const nextPayoutDate = account.next_payout_date ? new Date(account.next_payout_date) : null

      // Determine profit period
      const profitPeriodEnd = now
      const profitPeriodStart = this.getProfitPeriodStart(account, profitPeriodEnd)

      // Get trades for the profit period
      const { data: trades, error: tradesError } = await supabase
        .from('trades')
        .select('*')
        .eq('account_id', account.id)
        .eq('status', 'closed')
        .gte('close_time', profitPeriodStart.toISOString())
        .lte('close_time', profitPeriodEnd.toISOString())

      if (tradesError) {
        throw new Error('Failed to fetch trades for payout calculation')
      }

      // Calculate total profit for the period
      const totalProfit = trades?.reduce((sum, trade) => sum + (trade.profit || 0), 0) || 0

      // Determine payout eligibility and amount
      const payoutPercentage = account.payout_percentage || this.DEFAULT_PAYOUT_PERCENTAGE
      const payoutAmount = (totalProfit * payoutPercentage) / 100

      let eligibleForPayout = true
      let reason: string | undefined

      // Check eligibility criteria
      if (!nextPayoutDate || nextPayoutDate > now) {
        eligibleForPayout = false
        reason = 'Payout date not reached'
      } else if (totalProfit <= 0) {
        eligibleForPayout = false
        reason = 'No profit generated during period'
      } else if (payoutAmount < this.MIN_PAYOUT_AMOUNT) {
        eligibleForPayout = false
        reason = `Payout amount below minimum threshold ($${this.MIN_PAYOUT_AMOUNT})`
      }

      // Calculate next payout date
      const newNextPayoutDate = new Date(profitPeriodEnd)
      newNextPayoutDate.setDate(newNextPayoutDate.getDate() + (account.payout_frequency || 14))

      return {
        accountId: account.id,
        totalProfit,
        payoutAmount: Math.max(0, payoutAmount),
        payoutPercentage,
        profitPeriodStart,
        profitPeriodEnd,
        nextPayoutDate: newNextPayoutDate,
        eligibleForPayout,
        reason
      }
    } catch (error) {
      console.error('Error calculating account payout:', error)
      return {
        accountId: account.id,
        totalProfit: 0,
        payoutAmount: 0,
        payoutPercentage: this.DEFAULT_PAYOUT_PERCENTAGE,
        profitPeriodStart: new Date(),
        profitPeriodEnd: new Date(),
        nextPayoutDate: new Date(),
        eligibleForPayout: false,
        reason: 'Calculation error'
      }
    }
  }

  /**
   * Process payouts for eligible accounts
   */
  async processPayouts(calculations: PayoutCalculation[]): Promise<{
    processed: number
    failed: number
    totalAmount: number
    results: Array<{ accountId: string; success: boolean; message: string; payoutId?: string }>
  }> {
    const results = []
    let processed = 0
    let failed = 0
    let totalAmount = 0

    for (const calc of calculations) {
      if (!calc.eligibleForPayout) {
        results.push({
          accountId: calc.accountId,
          success: false,
          message: calc.reason || 'Not eligible for payout'
        })
        failed++
        continue
      }

      try {
        const result = await this.processSinglePayout(calc)
        results.push(result)
        
        if (result.success) {
          processed++
          totalAmount += calc.payoutAmount
        } else {
          failed++
        }
      } catch (error) {
        console.error(`Error processing payout for account ${calc.accountId}:`, error)
        results.push({
          accountId: calc.accountId,
          success: false,
          message: 'Processing error occurred'
        })
        failed++
      }
    }

    return {
      processed,
      failed,
      totalAmount,
      results
    }
  }

  /**
   * Process a single payout
   */
  private async processSinglePayout(calculation: PayoutCalculation): Promise<{
    accountId: string
    success: boolean
    message: string
    payoutId?: string
  }> {
    try {
      // Get account details
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('id', calculation.accountId)
        .single()

      if (accountError || !account) {
        return {
          accountId: calculation.accountId,
          success: false,
          message: 'Account not found'
        }
      }

      // Create payout record
      const payoutData: PayoutInsert = {
        user_id: account.user_id,
        account_id: calculation.accountId,
        amount: calculation.payoutAmount,
        profit_period_start: calculation.profitPeriodStart.toISOString(),
        profit_period_end: calculation.profitPeriodEnd.toISOString(),
        total_profit: calculation.totalProfit,
        payout_percentage: calculation.payoutPercentage,
        status: 'pending',
        description: `Automated payout for period ${calculation.profitPeriodStart.toDateString()} - ${calculation.profitPeriodEnd.toDateString()}`
      }

      const { data: payout, error: payoutError } = await supabase
        .from('payouts')
        .insert(payoutData)
        .select()
        .single()

      if (payoutError || !payout) {
        return {
          accountId: calculation.accountId,
          success: false,
          message: 'Failed to create payout record'
        }
      }

      // Update account's accumulated profit and next payout date
      const { error: updateError } = await supabase
        .from('trading_accounts')
        .update({
          accumulated_profit: 0, // Reset after payout
          next_payout_date: calculation.nextPayoutDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', calculation.accountId)

      if (updateError) {
        // Rollback payout record
        await supabase
          .from('payouts')
          .delete()
          .eq('id', payout.id)

        return {
          accountId: calculation.accountId,
          success: false,
          message: 'Failed to update account after payout creation'
        }
      }

      // Log payout event
      await this.logPayoutEvent(payout.id, 'payout_calculated', {
        amount: calculation.payoutAmount,
        profitPeriod: {
          start: calculation.profitPeriodStart.toISOString(),
          end: calculation.profitPeriodEnd.toISOString()
        },
        totalProfit: calculation.totalProfit,
        payoutPercentage: calculation.payoutPercentage
      })

      return {
        accountId: calculation.accountId,
        success: true,
        message: 'Payout processed successfully',
        payoutId: payout.id
      }
    } catch (error) {
      console.error('Error processing single payout:', error)
      return {
        accountId: calculation.accountId,
        success: false,
        message: 'Internal processing error'
      }
    }
  }

  /**
   * Get payout summary for dashboard
   */
  async getPayoutSummary(dateRange?: { start: Date; end: Date }): Promise<PayoutSummary> {
    try {
      const startDate = dateRange?.start || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      const endDate = dateRange?.end || new Date()

      // Get payouts for the period
      const { data: payouts, error: payoutsError } = await supabase
        .from('payouts')
        .select(`
          *,
          trading_account:trading_accounts(payout_frequency)
        `)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString())

      if (payoutsError) {
        throw new Error('Failed to fetch payout summary')
      }

      const payoutList = payouts || []
      const totalPayoutAmount = payoutList.reduce((sum, payout) => sum + payout.amount, 0)
      const totalProfitGenerated = payoutList.reduce((sum, payout) => sum + payout.total_profit, 0)
      const eligibleAccounts = new Set(payoutList.map(p => p.account_id)).size

      // Group by frequency
      const payoutsByFrequency: PayoutSummary['payoutsByFrequency'] = {}
      
      for (const payout of payoutList) {
        const frequency = (payout.trading_account as any)?.payout_frequency || 14
        if (!payoutsByFrequency[frequency]) {
          payoutsByFrequency[frequency] = { accounts: 0, totalPayout: 0 }
        }
        payoutsByFrequency[frequency].totalPayout += payout.amount
      }

      // Count unique accounts per frequency
      for (const frequency of this.VALID_FREQUENCIES) {
        const accountsForFreq = new Set(
          payoutList
            .filter(p => (p.trading_account as any)?.payout_frequency === frequency)
            .map(p => p.account_id)
        ).size
        
        if (!payoutsByFrequency[frequency]) {
          payoutsByFrequency[frequency] = { accounts: 0, totalPayout: 0 }
        }
        payoutsByFrequency[frequency].accounts = accountsForFreq
      }

      return {
        totalAccounts: payoutList.length,
        eligibleAccounts,
        totalPayoutAmount,
        totalProfitGenerated,
        averagePayoutAmount: eligibleAccounts > 0 ? totalPayoutAmount / eligibleAccounts : 0,
        payoutsByFrequency
      }
    } catch (error) {
      console.error('Error getting payout summary:', error)
      return {
        totalAccounts: 0,
        eligibleAccounts: 0,
        totalPayoutAmount: 0,
        totalProfitGenerated: 0,
        averagePayoutAmount: 0,
        payoutsByFrequency: {}
      }
    }
  }

  /**
   * Get user's payout history
   */
  async getUserPayoutHistory(userId: string, limit: number = 50): Promise<Payout[]> {
    try {
      const { data: payouts, error } = await supabase
        .from('payouts')
        .select(`
          *,
          trading_account:trading_accounts(account_nickname, account_number)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        throw new Error('Failed to fetch payout history')
      }

      return payouts || []
    } catch (error) {
      console.error('Error getting user payout history:', error)
      return []
    }
  }

  /**
   * Schedule next payout date for an account
   */
  async scheduleNextPayout(accountId: string, frequency?: number): Promise<void> {
    try {
      const { data: account, error: accountError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('id', accountId)
        .single()

      if (accountError || !account) {
        throw new Error('Account not found')
      }

      const payoutFrequency = frequency || account.payout_frequency || 14
      const nextPayoutDate = new Date()
      nextPayoutDate.setDate(nextPayoutDate.getDate() + payoutFrequency)

      await supabase
        .from('trading_accounts')
        .update({
          next_payout_date: nextPayoutDate.toISOString(),
          payout_frequency: payoutFrequency
        })
        .eq('id', accountId)
    } catch (error) {
      console.error('Error scheduling next payout:', error)
      throw error
    }
  }

  /**
   * Get profit period start date based on account's last payout
   */
  private getProfitPeriodStart(account: TradingAccount, periodEnd: Date): Date {
    // Get the last payout date, or account start date if no payouts
    const lastPayoutDate = this.getLastPayoutDate(account.id)
    const accountStartDate = new Date(account.start_date || account.created_at)
    
    // Use last payout date or account start date, whichever is more recent
    return lastPayoutDate > accountStartDate ? lastPayoutDate : accountStartDate
  }

  /**
   * Get the last payout date for an account
   */
  private async getLastPayoutDate(accountId: string): Promise<Date> {
    try {
      const { data: lastPayout } = await supabase
        .from('payouts')
        .select('profit_period_end')
        .eq('account_id', accountId)
        .order('profit_period_end', { ascending: false })
        .limit(1)
        .single()

      return lastPayout ? new Date(lastPayout.profit_period_end) : new Date(0)
    } catch (error) {
      return new Date(0) // Return epoch if no payouts found
    }
  }

  /**
   * Log payout events for audit trail
   */
  private async logPayoutEvent(payoutId: string, event: string, data: any): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          resource_type: 'payout',
          resource_id: payoutId,
          action: event,
          new_values: data
        })
    } catch (error) {
      console.error('Failed to log payout event:', error)
    }
  }

  /**
   * Run automated payout processing (to be called by cron job)
   */
  async runAutomatedPayoutProcessing(): Promise<{
    success: boolean
    message: string
    summary: {
      calculated: number
      processed: number
      failed: number
      totalAmount: number
    }
  }> {
    try {
      console.log('Starting automated payout processing...')
      
      // Calculate pending payouts
      const calculations = await this.calculatePendingPayouts()
      const eligibleCalculations = calculations.filter(calc => calc.eligibleForPayout)
      
      console.log(`Found ${calculations.length} accounts for payout calculation`)
      console.log(`${eligibleCalculations.length} accounts eligible for payout`)

      if (eligibleCalculations.length === 0) {
        return {
          success: true,
          message: 'No accounts eligible for payout at this time',
          summary: {
            calculated: calculations.length,
            processed: 0,
            failed: 0,
            totalAmount: 0
          }
        }
      }

      // Process payouts
      const processingResult = await this.processPayouts(eligibleCalculations)
      
      console.log(`Payout processing completed: ${processingResult.processed} processed, ${processingResult.failed} failed`)

      return {
        success: true,
        message: `Processed ${processingResult.processed} payouts totaling $${processingResult.totalAmount.toFixed(2)}`,
        summary: {
          calculated: calculations.length,
          processed: processingResult.processed,
          failed: processingResult.failed,
          totalAmount: processingResult.totalAmount
        }
      }
    } catch (error) {
      console.error('Error in automated payout processing:', error)
      return {
        success: false,
        message: 'Automated payout processing failed',
        summary: {
          calculated: 0,
          processed: 0,
          failed: 0,
          totalAmount: 0
        }
      }
    }
  }
}

// Export singleton instance
export const payoutService = new PayoutService()