'use client'

import { getTradingViewWebSocketClient, TradingViewCandle, TradingViewQuote } from './tradingViewWebSocketClient'
import { getMockTradingDataService } from './mockTradingDataService'

// Browser-compatible TradingView interfaces
export interface TradingViewCandle {
  time: number // Unix timestamp in seconds
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TradingViewQuote {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  timestamp: number // Unix timestamp in seconds
}

export interface TradingViewIndicatorData {
  time: number
  value: number
}

// Configuration interface
interface TradingViewBrowserConfig {
  reconnectAttempts: number
  reconnectDelay: number
  enableLogging: boolean
  enableMockData: boolean
  mockDataInterval: number
}

// Timeframe mapping
const TIMEFRAME_MAP: { [key: string]: string } = {
  '1M': '1m',
  '5M': '5m', 
  '15M': '15m',
  '1H': '1h',
  '4H': '4h',
  '1D': '1d',
  '1W': '1w',
  '1MONTH': '1M'
}

/**
 * Browser-Compatible TradingView Service
 * 
 * This service provides a browser-safe implementation for TradingView data
 * with automatic fallback to mock data when real connections fail.
 * 
 * Key Features:
 * - Uses native WebSocket (browser-compatible)
 * - Automatic fallback to mock data
 * - Error boundaries and graceful degradation
 * - Real-time data streaming
 * - Historical data access
 */
export class TradingViewBrowserService {
  private webSocketClient: any = null
  private mockDataService: any = null
  private connected: boolean = false
  private config: TradingViewBrowserConfig
  private subscriptions: Set<string> = new Set()
  private dataCallbacks: Map<string, Function[]> = new Map()
  private errorCallback: ((error: Error) => void) | null = null
  private mockDataIntervals: Map<string, NodeJS.Timeout> = new Map()
  private lastPrices: Map<string, number> = new Map()
  private connectionAttempts: number = 0
  private usingMockData: boolean = false

  constructor(config: Partial<TradingViewBrowserConfig> = {}) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      enableLogging: process.env.NODE_ENV === 'development',
      enableMockData: true, // Enable mock data by default for reliability
      mockDataInterval: 1000, // Update mock data every second
      ...config
    }

    if (this.config.enableLogging) {
      console.log('📊 [TradingViewBrowserService] Initialized with config:', this.config)
    }
  }

  /**
   * Connect to TradingView data sources
   * Uses WebSocket client with fallback to mock data
   */
  async connect(): Promise<void> {
    this.connectionAttempts++
    
    try {
      if (this.config.enableLogging) {
        console.log('📊 [TradingViewBrowserService] Attempting connection (attempt', this.connectionAttempts, ')...')
      }

      // Try WebSocket connection first
      this.webSocketClient = getTradingViewWebSocketClient()
      
      if (!this.webSocketClient) {
        throw new Error('WebSocket client not available')
      }

      // Set up error handling
      this.webSocketClient.onError((error: Error) => {
        console.warn('⚠️ [TradingViewBrowserService] WebSocket error:', error.message)
        if (this.errorCallback) {
          this.errorCallback(error)
        }
        
        // Fall back to mock data on error
        if (this.config.enableMockData) {
          this.startMockDataMode()
        }
      })

      // Attempt WebSocket connection
      await this.webSocketClient.connect()
      
      if (this.webSocketClient.isConnected()) {
        this.connected = true
        this.connectionAttempts = 0
        
        if (this.config.enableLogging) {
          console.log('✅ [TradingViewBrowserService] WebSocket connection established')
        }
        
        // Stop any running mock data
        this.stopMockDataMode()
        
        return
      }
      
      throw new Error('WebSocket connection failed')
      
    } catch (error) {
      console.warn('⚠️ [TradingViewBrowserService] Connection failed:', error)
      
      // Use enhanced mock data service as fallback
      if (this.config.enableMockData) {
        try {
          this.mockDataService = getMockTradingDataService()
          this.usingMockData = true
          this.connected = true // Consider mock mode as "connected"
          
          if (this.config.enableLogging) {
            console.log('✅ [TradingViewBrowserService] Using enhanced mock data service as fallback')
          }
          
          return
        } catch (mockError) {
          console.error('❌ [TradingViewBrowserService] Failed to initialize mock data service:', mockError)
          // Fall back to original mock mode
          this.startMockDataMode()
          this.connected = true
          
          if (this.config.enableLogging) {
            console.log('📊 [TradingViewBrowserService] Using basic mock data mode as final fallback')
          }
          
          return
        }
      }
      
      throw new Error(`TradingView connection failed: ${error}`)
    }
  }

  /**
   * Disconnect from all data sources
   */
  disconnect(): void {
    this.connected = false
    
    if (this.webSocketClient) {
      try {
        this.webSocketClient.disconnect()
      } catch (error) {
        console.warn('⚠️ [TradingViewBrowserService] Error disconnecting WebSocket:', error)
      }
      this.webSocketClient = null
    }
    
    // Clean up mock data service
    if (this.mockDataService) {
      try {
        this.mockDataService.cleanup()
      } catch (error) {
        console.warn('⚠️ [TradingViewBrowserService] Error cleaning up mock data service:', error)
      }
      this.mockDataService = null
    }
    
    this.stopMockDataMode()
    this.subscriptions.clear()
    this.dataCallbacks.clear()
    this.usingMockData = false
  }

  /**
   * Check if connected (including mock mode)
   */
  isConnected(): boolean {
    return this.connected
  }

  /**
   * Subscribe to real-time data for a symbol
   */
  async subscribeToSymbol(symbol: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to TradingView data sources')
    }

    const normalizedSymbol = this.normalizeSymbol(symbol)
    this.subscriptions.add(normalizedSymbol)

    if (this.config.enableLogging) {
      console.log(`📊 [TradingViewBrowserService] Subscribing to ${normalizedSymbol}`)
    }

    // Try WebSocket subscription first
    if (this.webSocketClient && this.webSocketClient.isConnected()) {
      try {
        this.webSocketClient.subscribeToSymbol(normalizedSymbol)
        return
      } catch (error) {
        console.warn('⚠️ [TradingViewBrowserService] WebSocket subscription failed:', error)
      }
    }

    // Fall back to enhanced mock data service or basic mock data
    if (this.config.enableMockData) {
      if (this.usingMockData && this.mockDataService) {
        // Use enhanced mock data service
        const quoteCallbacks = this.dataCallbacks.get(normalizedSymbol) || []
        quoteCallbacks.forEach(callback => {
          this.mockDataService.subscribeToQuotes(normalizedSymbol, callback)
        })
      } else {
        // Use basic mock data mode
        this.subscribeMockData(normalizedSymbol)
      }
    }
  }

  /**
   * Get historical candlestick data
   */
  async getCandlestickData(
    symbol: string, 
    timeframe: string, 
    limit: number = 1000
  ): Promise<TradingViewCandle[]> {
    if (!this.isConnected()) {
      throw new Error('Not connected to TradingView data sources')
    }

    const normalizedSymbol = this.normalizeSymbol(symbol)
    const tvTimeframe = TIMEFRAME_MAP[timeframe] || timeframe

    if (this.config.enableLogging) {
      console.log(`📊 [TradingViewBrowserService] Fetching candle data: ${normalizedSymbol}, ${tvTimeframe}, ${limit} bars`)
    }

    // Try WebSocket client first
    if (this.webSocketClient && this.webSocketClient.isConnected()) {
      try {
        const data = await this.webSocketClient.getCandlestickData(normalizedSymbol, tvTimeframe, limit)
        if (data && data.length > 0) {
          return data
        }
      } catch (error) {
        console.warn('⚠️ [TradingViewBrowserService] WebSocket candle data failed:', error)
      }
    }

    // Fall back to enhanced mock data service or basic mock data
    if (this.config.enableMockData) {
      if (this.usingMockData && this.mockDataService) {
        // Use enhanced mock data service
        return this.mockDataService.generateHistoricalCandles(normalizedSymbol, timeframe, limit)
      } else {
        // Use basic mock data mode
        return this.generateMockCandleData(normalizedSymbol, timeframe, limit)
      }
    }

    throw new Error('No data source available for candlestick data')
  }

  /**
   * Add callback for real-time quote data
   */
  onSymbolData(symbol: string, callback: (data: TradingViewQuote) => void): void {
    const normalizedSymbol = this.normalizeSymbol(symbol)
    
    if (!this.dataCallbacks.has(normalizedSymbol)) {
      this.dataCallbacks.set(normalizedSymbol, [])
    }
    
    this.dataCallbacks.get(normalizedSymbol)!.push(callback)

    // Set up WebSocket callback if available
    if (this.webSocketClient && this.webSocketClient.isConnected()) {
      this.webSocketClient.onSymbolData(normalizedSymbol, callback)
    }

    // Auto-subscribe if connected
    if (this.isConnected() && !this.subscriptions.has(normalizedSymbol)) {
      this.subscribeToSymbol(symbol).catch(console.error)
    }
  }

  /**
   * Remove callback for symbol data
   */
  offSymbolData(symbol: string, callback: (data: TradingViewQuote) => void): void {
    const normalizedSymbol = this.normalizeSymbol(symbol)
    const callbacks = this.dataCallbacks.get(normalizedSymbol) || []
    const index = callbacks.indexOf(callback)
    
    if (index > -1) {
      callbacks.splice(index, 1)
    }

    // Remove WebSocket callback if available
    if (this.webSocketClient) {
      this.webSocketClient.offSymbolData(normalizedSymbol, callback)
    }
  }

  /**
   * Set error callback
   */
  onError(callback: (error: Error) => void): void {
    this.errorCallback = callback
  }

  /**
   * Add callback for candlestick updates
   */
  onCandlestickUpdate(symbol: string, timeframe: string, callback: (candle: TradingViewCandle) => void): void {
    const normalizedSymbol = this.normalizeSymbol(symbol)
    const key = `${normalizedSymbol}_candles`
    
    if (!this.dataCallbacks.has(key)) {
      this.dataCallbacks.set(key, [])
    }
    
    this.dataCallbacks.get(key)!.push(callback)

    // Auto-subscribe if connected
    if (this.isConnected() && !this.subscriptions.has(normalizedSymbol)) {
      this.subscribeToSymbol(symbol).catch(console.error)
    }
  }

  /**
   * Remove candlestick update callback
   */
  offCandlestickUpdate(symbol: string, timeframe: string, callback: (candle: TradingViewCandle) => void): void {
    const normalizedSymbol = this.normalizeSymbol(symbol)
    const key = `${normalizedSymbol}_candles`
    const callbacks = this.dataCallbacks.get(key) || []
    const index = callbacks.indexOf(callback as any)
    
    if (index > -1) {
      callbacks.splice(index, 1)
    }
  }

  /**
   * Get available symbols
   */
  async getAvailableSymbols(): Promise<string[]> {
    return [
      'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD', 'NZD/USD',
      'GOLD', 'SILVER', 'OIL', 'NATGAS',
      'SPX500', 'US30', 'NAS100', 'UK100',
      'BTCUSD', 'ETHUSD', 'XRPUSD'
    ]
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    this.disconnect()
    this.dataCallbacks.clear()
    this.subscriptions.clear()
    this.errorCallback = null
    this.stopMockDataMode()
  }

  // PRIVATE METHODS

  /**
   * Start mock data mode for fallback
   */
  private startMockDataMode(): void {
    if (this.config.enableLogging) {
      console.log('📊 [TradingViewBrowserService] Starting mock data mode')
    }

    // Clear existing mock intervals
    this.stopMockDataMode()

    // Generate mock data for all subscribed symbols
    this.subscriptions.forEach(symbol => {
      this.subscribeMockData(symbol)
    })
  }

  /**
   * Stop mock data mode
   */
  private stopMockDataMode(): void {
    this.mockDataIntervals.forEach(interval => {
      clearInterval(interval)
    })
    this.mockDataIntervals.clear()
  }

  /**
   * Subscribe to mock data for a symbol
   */
  private subscribeMockData(symbol: string): void {
    // Initialize price if not exists
    if (!this.lastPrices.has(symbol)) {
      this.lastPrices.set(symbol, this.getInitialPrice(symbol))
    }

    // Clear existing interval for this symbol
    const existingInterval = this.mockDataIntervals.get(symbol)
    if (existingInterval) {
      clearInterval(existingInterval)
    }

    // Create new mock data interval
    const interval = setInterval(() => {
      this.generateMockQuote(symbol)
    }, this.config.mockDataInterval)

    this.mockDataIntervals.set(symbol, interval)

    if (this.config.enableLogging) {
      console.log(`📊 [TradingViewBrowserService] Mock data subscription started for ${symbol}`)
    }
  }

  /**
   * Generate mock quote data
   */
  private generateMockQuote(symbol: string): void {
    const lastPrice = this.lastPrices.get(symbol) || this.getInitialPrice(symbol)
    
    // Generate realistic price movement (±0.1% max change)
    const changePercent = (Math.random() - 0.5) * 0.002 // ±0.1%
    const newPrice = lastPrice * (1 + changePercent)
    
    this.lastPrices.set(symbol, newPrice)

    const quote: TradingViewQuote = {
      symbol,
      price: newPrice,
      change: newPrice - lastPrice,
      changePercent: changePercent * 100,
      volume: Math.floor(Math.random() * 1000000),
      timestamp: Math.floor(Date.now() / 1000)
    }

    // Send to quote callbacks
    const callbacks = this.dataCallbacks.get(symbol) || []
    callbacks.forEach(callback => {
      try {
        callback(quote)
      } catch (error) {
        console.warn('⚠️ [TradingViewBrowserService] Error in quote callback:', error)
      }
    })

    // Send to candle callbacks
    const candleCallbacks = this.dataCallbacks.get(`${symbol}_candles`) || []
    if (candleCallbacks.length > 0) {
      const candle: TradingViewCandle = {
        time: quote.timestamp,
        open: lastPrice,
        high: Math.max(lastPrice, newPrice),
        low: Math.min(lastPrice, newPrice),
        close: newPrice,
        volume: quote.volume
      }

      candleCallbacks.forEach(callback => {
        try {
          callback(candle)
        } catch (error) {
          console.warn('⚠️ [TradingViewBrowserService] Error in candle callback:', error)
        }
      })
    }
  }

  /**
   * Generate mock historical candle data
   */
  private generateMockCandleData(symbol: string, timeframe: string, limit: number): TradingViewCandle[] {
    const candles: TradingViewCandle[] = []
    const basePrice = this.getInitialPrice(symbol)
    const timeframeMs = this.getTimeframeMs(timeframe)
    const now = Date.now()
    
    let currentPrice = basePrice
    
    for (let i = limit - 1; i >= 0; i--) {
      const time = Math.floor((now - (i * timeframeMs)) / 1000)
      
      // Generate realistic OHLC data
      const changePercent = (Math.random() - 0.5) * 0.02 // ±1% max change per candle
      const open = currentPrice
      const close = open * (1 + changePercent)
      const high = Math.max(open, close) * (1 + Math.random() * 0.01)
      const low = Math.min(open, close) * (1 - Math.random() * 0.01)
      const volume = Math.floor(Math.random() * 1000000) + 100000
      
      candles.push({
        time,
        open,
        high,
        low,
        close,
        volume
      })
      
      currentPrice = close
    }
    
    // Update last price for future mock data
    this.lastPrices.set(symbol, currentPrice)
    
    return candles
  }

  /**
   * Get initial price for a symbol
   */
  private getInitialPrice(symbol: string): number {
    const priceMap: { [key: string]: number } = {
      'EUR/USD': 1.0950,
      'GBP/USD': 1.2750,
      'USD/JPY': 149.50,
      'USD/CHF': 0.8850,
      'AUD/USD': 0.6650,
      'USD/CAD': 1.3550,
      'NZD/USD': 0.6150,
      'GOLD': 2650.00,
      'SILVER': 31.50,
      'OIL': 75.50,
      'NATGAS': 3.25,
      'SPX500': 4750.00,
      'US30': 37500.00,
      'NAS100': 16500.00,
      'UK100': 7800.00,
      'BTCUSD': 65000.00,
      'ETHUSD': 3200.00,
      'XRPUSD': 0.55
    }

    return priceMap[symbol] || 1.0000
  }

  /**
   * Get timeframe in milliseconds
   */
  private getTimeframeMs(timeframe: string): number {
    const timeframeMap: { [key: string]: number } = {
      '1M': 60 * 1000,
      '5M': 5 * 60 * 1000,
      '15M': 15 * 60 * 1000,
      '1H': 60 * 60 * 1000,
      '4H': 4 * 60 * 60 * 1000,
      '1D': 24 * 60 * 60 * 1000,
      '1W': 7 * 24 * 60 * 60 * 1000,
      '1MONTH': 30 * 24 * 60 * 60 * 1000
    }

    return timeframeMap[timeframe] || 60 * 1000
  }

  /**
   * Normalize symbol names
   */
  private normalizeSymbol(symbol: string): string {
    // Convert common symbol formats to TradingView format
    const symbolMap: { [key: string]: string } = {
      'EUR/USD': 'FX_IDC:EURUSD',
      'GBP/USD': 'FX_IDC:GBPUSD', 
      'USD/JPY': 'FX_IDC:USDJPY',
      'USD/CHF': 'FX_IDC:USDCHF',
      'AUD/USD': 'FX_IDC:AUDUSD',
      'USD/CAD': 'FX_IDC:USDCAD',
      'NZD/USD': 'FX_IDC:NZDUSD',
      'GOLD': 'TVC:GOLD',
      'SILVER': 'TVC:SILVER',
      'OIL': 'TVC:USOIL',
      'NATGAS': 'TVC:NATURALGAS',
      'SPX500': 'SP:SPX',
      'US30': 'TVC:DJI',
      'NAS100': 'TVC:IXIC',
      'UK100': 'TVC:UKX',
      'BTCUSD': 'BINANCE:BTCUSDT',
      'ETHUSD': 'BINANCE:ETHUSDT',
      'XRPUSD': 'COINBASE:XRPUSD'
    }

    return symbolMap[symbol] || symbol
  }
}

// Create singleton instance
let tradingViewBrowserService: TradingViewBrowserService | null = null

export const getTradingViewBrowserService = (): TradingViewBrowserService => {
  if (!tradingViewBrowserService) {
    try {
      tradingViewBrowserService = new TradingViewBrowserService()
      console.log('📊 [getTradingViewBrowserService] Created new TradingViewBrowserService instance')
    } catch (error) {
      console.error('📊 [getTradingViewBrowserService] Failed to create TradingViewBrowserService:', error)
      throw error
    }
  }
  return tradingViewBrowserService
}

export default TradingViewBrowserService