'use client'

/**
 * WebSocket Monitor Component
 * 
 * Provides real-time monitoring of WebSocket connections, error tracking,
 * and performance metrics for the trading platform.
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Database,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Wifi,
  WifiOff,
  Zap
} from 'lucide-react'

interface WebSocketMetrics {
  totalConnections: number
  successfulConnections: number
  failedConnections: number
  totalReconnects: number
  avgConnectionTime: number
  uptime: number
  lastError?: {
    type: string
    message: string
    timestamp: number
  }
}

interface ConnectionHealth {
  isConnected: boolean
  state: string
  latency: number
  messageRate: number
  errorRate: number
  lastHeartbeat: number
}

interface WebSocketMonitorProps {
  wsManager?: any // WebSocketManager instance
  fallbackPipeline?: any // FallbackDataPipeline instance
  onRetryConnection?: () => void
  className?: string
}

export const WebSocketMonitor: React.FC<WebSocketMonitorProps> = ({
  wsManager,
  fallbackPipeline,
  onRetryConnection,
  className = ''
}) => {
  const [metrics, setMetrics] = useState<WebSocketMetrics>({
    totalConnections: 0,
    successfulConnections: 0,
    failedConnections: 0,
    totalReconnects: 0,
    avgConnectionTime: 0,
    uptime: 0
  })

  const [health, setHealth] = useState<ConnectionHealth>({
    isConnected: false,
    state: 'disconnected',
    latency: 0,
    messageRate: 0,
    errorRate: 0,
    lastHeartbeat: 0
  })

  const [isExpanded, setIsExpanded] = useState(false)
  const [realtimeData, setRealtimeData] = useState<any[]>([])

  // Update metrics from WebSocket manager
  const updateMetrics = useCallback(() => {
    if (wsManager) {
      try {
        const currentMetrics = wsManager.getMetrics()
        setMetrics(currentMetrics)
        
        setHealth(prevHealth => ({
          ...prevHealth,
          isConnected: wsManager.isConnected(),
          state: wsManager.getState()
        }))
      } catch (error) {
        console.warn('⚠️ [WebSocketMonitor] Error updating metrics:', error)
      }
    }
  }, [wsManager])

  // Track real-time message activity
  const trackMessageActivity = useCallback((message: any) => {
    setRealtimeData(prev => {
      const newData = [...prev, {
        timestamp: Date.now(),
        type: message.type,
        symbol: message.symbol,
        size: JSON.stringify(message).length
      }].slice(-50) // Keep last 50 messages
      
      return newData
    })
    
    // Update message rate
    const now = Date.now()
    const recentMessages = realtimeData.filter(data => now - data.timestamp < 10000) // Last 10 seconds
    const messageRate = recentMessages.length / 10 // Messages per second
    
    setHealth(prev => ({
      ...prev,
      messageRate,
      lastHeartbeat: now
    }))
  }, [realtimeData])

  // Set up event listeners
  useEffect(() => {
    if (!wsManager) return

    // Listen to WebSocket manager events
    wsManager.on('message', trackMessageActivity)
    wsManager.on('connected', updateMetrics)
    wsManager.on('disconnected', updateMetrics)
    wsManager.on('error', updateMetrics)
    wsManager.on('state_change', updateMetrics)

    // Update metrics periodically
    const metricsInterval = setInterval(updateMetrics, 1000)

    return () => {
      wsManager.off('message', trackMessageActivity)
      wsManager.off('connected', updateMetrics)
      wsManager.off('disconnected', updateMetrics)
      wsManager.off('error', updateMetrics)
      wsManager.off('state_change', updateMetrics)
      clearInterval(metricsInterval)
    }
  }, [wsManager, updateMetrics, trackMessageActivity])

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`
    return `${seconds}s`
  }

  const getConnectionStatusColor = () => {
    switch (health.state) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'connecting':
      case 'reconnecting':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'disconnected':
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSuccessRate = () => {
    if (metrics.totalConnections === 0) return 0
    return Math.round((metrics.successfulConnections / metrics.totalConnections) * 100)
  }

  const getHealthScore = () => {
    let score = 0
    
    // Connection status (40%)
    if (health.isConnected) score += 40
    
    // Success rate (30%)
    score += (getSuccessRate() / 100) * 30
    
    // Message rate (20%) - good if receiving messages
    if (health.messageRate > 0) score += 20
    
    // Error rate (10%) - deduct for errors
    if (health.errorRate < 0.1) score += 10
    
    return Math.round(score)
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg border ${getConnectionStatusColor()}`}>
              {health.isConnected ? (
                <Wifi className="h-5 w-5" />
              ) : (
                <WifiOff className="h-5 w-5" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                WebSocket Monitor
              </h3>
              <p className="text-sm text-gray-600">
                Connection Health: {getHealthScore()}% • {health.state}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {onRetryConnection && !health.isConnected && (
              <button
                onClick={onRetryConnection}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Retry
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? 'Hide Details' : 'Show Details'}
            </button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {getSuccessRate()}%
            </div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {formatDuration(metrics.uptime)}
            </div>
            <div className="text-sm text-gray-600">Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {health.messageRate.toFixed(1)}/s
            </div>
            <div className="text-sm text-gray-600">Message Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {metrics.totalReconnects}
            </div>
            <div className="text-sm text-gray-600">Reconnects</div>
          </div>
        </div>
      </div>

      {/* Detailed Information */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Connection Statistics */}
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  Connection Statistics
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Connections:</span>
                    <span className="font-medium">{metrics.totalConnections}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Successful:</span>
                    <span className="font-medium text-green-600">
                      {metrics.successfulConnections}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Failed:</span>
                    <span className="font-medium text-red-600">
                      {metrics.failedConnections}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Connection Time:</span>
                    <span className="font-medium">
                      {metrics.avgConnectionTime.toFixed(0)}ms
                    </span>
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div>
                <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Performance Metrics
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Latency:</span>
                    <span className="font-medium">{health.latency}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Error Rate:</span>
                    <span className="font-medium">
                      {(health.errorRate * 100).toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Last Heartbeat:</span>
                    <span className="font-medium">
                      {health.lastHeartbeat > 0 
                        ? formatDuration(Date.now() - health.lastHeartbeat) + ' ago'
                        : 'Never'
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Health Score:</span>
                    <span className={`font-medium ${
                      getHealthScore() >= 80 ? 'text-green-600' :
                      getHealthScore() >= 60 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {getHealthScore()}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Last Error */}
            {metrics.lastError && (
              <div className="mt-6 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-red-800">
                      Last Error ({metrics.lastError.type})
                    </div>
                    <div className="text-sm text-red-700 mt-1">
                      {metrics.lastError.message}
                    </div>
                    <div className="text-xs text-red-600 mt-1">
                      {new Date(metrics.lastError.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Real-time Activity */}
            {realtimeData.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Recent Activity (Last {realtimeData.length} messages)
                </h4>
                <div className="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                  <div className="space-y-1 text-xs font-mono">
                    {realtimeData.slice(-10).reverse().map((data, index) => (
                      <div key={index} className="flex justify-between text-gray-600">
                        <span>
                          {new Date(data.timestamp).toLocaleTimeString()} 
                          - {data.type} {data.symbol && `(${data.symbol})`}
                        </span>
                        <span>{data.size}b</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Fallback Status */}
            {fallbackPipeline && (
              <div className="mt-6">
                <h4 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                  <Database className="h-4 w-4 mr-2" />
                  Fallback Pipeline
                </h4>
                <div className="flex items-center space-x-2">
                  {fallbackPipeline.isFallbackActive() ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-yellow-700">
                        Fallback Active - Using alternative data sources
                      </span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-700">
                        Live Data - Primary connection active
                      </span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}