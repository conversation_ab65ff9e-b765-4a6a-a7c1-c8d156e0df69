'use client'

import { useState, useEffect, useCallback } from 'react'

interface ConnectedAccountInfo {
  stripeAccountId: string
  accountStatus: 'active' | 'pending' | 'restricted' | 'rejected'
  requiresVerification: boolean
  missingFields: string[]
}

interface PayoutEstimate {
  payoutAmount: number
  stripeFee: number
  netAmount: number
  estimatedArrival: string
}

interface UseStripeConnectReturn {
  accountInfo: ConnectedAccountInfo | null
  loading: boolean
  error: string | null
  actions: {
    refreshAccountInfo: () => Promise<void>
    createConnectAccount: (userInfo: {
      firstName: string
      lastName: string
      country: string
      businessType?: 'individual' | 'company'
    }) => Promise<{ success: boolean; message: string; onboardingUrl?: string }>
    getPayoutEstimate: (amount: number) => Promise<PayoutEstimate | null>
    processPayout: (payoutId: string) => Promise<{ success: boolean; message: string }>
    batchProcessPayouts: (payoutIds: string[]) => Promise<{ success: boolean; message: string; data?: any }>
  }
}

export function useStripeConnect(): UseStripeConnectReturn {
  const [accountInfo, setAccountInfo] = useState<ConnectedAccountInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Refresh account info from the server
  const refreshAccountInfo = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/stripe?action=account_info')
      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch account info')
      }

      setAccountInfo(data.data)
    } catch (err) {
      console.error('Error refreshing account info:', err)
      setError(err instanceof Error ? err.message : 'Failed to load account info')
      setAccountInfo(null)
    } finally {
      setLoading(false)
    }
  }, [])

  // Create or update Stripe Connect account
  const createConnectAccount = useCallback(async (userInfo: {
    firstName: string
    lastName: string
    country: string
    businessType?: 'individual' | 'company'
  }) => {
    try {
      const response = await fetch('/api/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'create_connect_account',
          ...userInfo
        })
      })

      const data = await response.json()

      if (data.success && data.onboardingUrl) {
        // Redirect to Stripe onboarding
        window.location.href = data.onboardingUrl
      }

      return {
        success: data.success,
        message: data.message,
        onboardingUrl: data.onboardingUrl
      }
    } catch (err) {
      console.error('Error creating connect account:', err)
      return {
        success: false,
        message: err instanceof Error ? err.message : 'Failed to create connect account'
      }
    }
  }, [])

  // Get payout estimate with fees
  const getPayoutEstimate = useCallback(async (amount: number): Promise<PayoutEstimate | null> => {
    try {
      if (amount <= 0) {
        return null
      }

      const response = await fetch(`/api/stripe?action=payout_estimate&amount=${amount}`)
      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'Failed to get payout estimate')
      }

      return data.data
    } catch (err) {
      console.error('Error getting payout estimate:', err)
      return null
    }
  }, [])

  // Process a single payout
  const processPayout = useCallback(async (payoutId: string) => {
    try {
      const response = await fetch('/api/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'process_payout',
          payoutId
        })
      })

      const data = await response.json()

      return {
        success: data.success,
        message: data.message || (data.success ? 'Payout processed successfully' : 'Failed to process payout')
      }
    } catch (err) {
      console.error('Error processing payout:', err)
      return {
        success: false,
        message: err instanceof Error ? err.message : 'Failed to process payout'
      }
    }
  }, [])

  // Process multiple payouts in batch
  const batchProcessPayouts = useCallback(async (payoutIds: string[]) => {
    try {
      if (payoutIds.length === 0) {
        return {
          success: false,
          message: 'No payouts selected'
        }
      }

      const response = await fetch('/api/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'batch_process_payouts',
          payoutIds
        })
      })

      const data = await response.json()

      return {
        success: data.success,
        message: data.message,
        data: data.data
      }
    } catch (err) {
      console.error('Error batch processing payouts:', err)
      return {
        success: false,
        message: err instanceof Error ? err.message : 'Failed to process payouts'
      }
    }
  }, [])

  // Initial load
  useEffect(() => {
    refreshAccountInfo()
  }, [refreshAccountInfo])

  return {
    accountInfo,
    loading,
    error,
    actions: {
      refreshAccountInfo,
      createConnectAccount,
      getPayoutEstimate,
      processPayout,
      batchProcessPayouts
    }
  }
}