/**
 * Market Data Lookup Service
 * Provides real-time market data for trade execution and P&L calculations
 */

import { supabase } from '@/lib/supabase/client'
import { symbolMappingService } from './symbolMappingService'

export interface MarketPrice {
  symbol: string
  bid: number
  ask: number
  last: number
  spread: number
  timestamp: string
}

export interface MarketDataStore {
  [symbol: string]: MarketPrice
}

class MarketDataLookupService {
  private static instance: MarketDataLookupService
  private marketDataStore: MarketDataStore = {}
  private webSocketConnected = false
  private fallbackPrices: MarketDataStore = {}
  private activeSymbols: Set<string> = new Set() // Track symbols that need live data
  private symbolSubscribers: Map<string, Set<string>> = new Map() // Track what's subscribing to each symbol
  private priceUpdateCallbacks: Map<string, Array<(price: MarketPrice) => void>> = new Map() // Real-time update callbacks

  private constructor() {
    this.initializeFallbackPrices()
  }

  public static getInstance(): MarketDataLookupService {
    if (!MarketDataLookupService.instance) {
      MarketDataLookupService.instance = new MarketDataLookupService()
    }
    return MarketDataLookupService.instance
  }

  /**
   * Initialize realistic fallback prices for major symbols
   */
  private initializeFallbackPrices() {
    this.fallbackPrices = {
      'EUR/USD': { symbol: 'EUR/USD', bid: 1.0845, ask: 1.0847, last: 1.0846, spread: 0.0002, timestamp: new Date().toISOString() },
      'GBP/USD': { symbol: 'GBP/USD', bid: 1.2756, ask: 1.2758, last: 1.2757, spread: 0.0002, timestamp: new Date().toISOString() },
      'USD/JPY': { symbol: 'USD/JPY', bid: 149.85, ask: 149.87, last: 149.86, spread: 0.02, timestamp: new Date().toISOString() },
      'USD/CHF': { symbol: 'USD/CHF', bid: 0.8923, ask: 0.8925, last: 0.8924, spread: 0.0002, timestamp: new Date().toISOString() },
      'AUD/USD': { symbol: 'AUD/USD', bid: 0.6587, ask: 0.6589, last: 0.6588, spread: 0.0002, timestamp: new Date().toISOString() },
      'USD/CAD': { symbol: 'USD/CAD', bid: 1.3845, ask: 1.3847, last: 1.3846, spread: 0.0002, timestamp: new Date().toISOString() },
      'NZD/USD': { symbol: 'NZD/USD', bid: 0.5923, ask: 0.5925, last: 0.5924, spread: 0.0002, timestamp: new Date().toISOString() },
      'XBRUSD': { symbol: 'XBRUSD', bid: 73.45, ask: 73.47, last: 73.46, spread: 0.02, timestamp: new Date().toISOString() },
      'GOLD': { symbol: 'GOLD', bid: 2034.50, ask: 2034.70, last: 2034.60, spread: 0.20, timestamp: new Date().toISOString() },
      'SILVER': { symbol: 'SILVER', bid: 24.12, ask: 24.14, last: 24.13, spread: 0.02, timestamp: new Date().toISOString() },
      'OIL': { symbol: 'OIL', bid: 81.23, ask: 81.25, last: 81.24, spread: 0.02, timestamp: new Date().toISOString() },
      'NATGAS': { symbol: 'NATGAS', bid: 2.845, ask: 2.847, last: 2.846, spread: 0.002, timestamp: new Date().toISOString() },
      'SPX500': { symbol: 'SPX500', bid: 4567.80, ask: 4567.90, last: 4567.85, spread: 0.10, timestamp: new Date().toISOString() },
      'US30': { symbol: 'US30', bid: 34567.8, ask: 34568.2, last: 34568.0, spread: 0.4, timestamp: new Date().toISOString() },
      'NAS100': { symbol: 'NAS100', bid: 15234.5, ask: 15234.8, last: 15234.65, spread: 0.3, timestamp: new Date().toISOString() },
      'UK100': { symbol: 'UK100', bid: 7456.2, ask: 7456.5, last: 7456.35, spread: 0.3, timestamp: new Date().toISOString() },
      'BTCUSD': { symbol: 'BTCUSD', bid: 42850.5, ask: 42851.5, last: 42851.0, spread: 1.0, timestamp: new Date().toISOString() },
      'ETHUSD': { symbol: 'ETHUSD', bid: 2456.80, ask: 2457.20, last: 2457.0, spread: 0.40, timestamp: new Date().toISOString() },
      'XRPUSD': { symbol: 'XRPUSD', bid: 2.8350, ask: 2.8370, last: 2.8360, spread: 0.0020, timestamp: new Date().toISOString() }
    }
  }

  /**
   * Update market data from WebSocket
   */
  public updateMarketData(data: any) {
    const price: MarketPrice = {
      symbol: data.symbol,
      bid: data.bid,
      ask: data.ask,
      last: data.last || data.bid, // Use bid as last if not provided
      spread: data.ask - data.bid,
      timestamp: data.timestamp || new Date().toISOString()
    }
    
    this.marketDataStore[data.symbol] = price
    this.webSocketConnected = true
  }

  /**
   * Update market data from TradingView WebSocket
   * Bridge function to connect TradingView data to position tracking
   * CRITICAL FIX: Converts TradingView normalized symbols to display symbols for consistency
   */
  public updateFromTradingView(data: any) {
    console.log(`🔗 [MarketDataLookup] Received TradingView data:`, data)
    
    const rawSymbol = (data.symbol || data.s || '').toUpperCase()
    if (!rawSymbol) {
      console.warn(`⚠️ [MarketDataLookup] No symbol in TradingView data:`, data)
      return
    }
    
    // CRITICAL FIX: Convert TradingView normalized symbol to display symbol
    // This ensures position tracking can find the market data
    const displaySymbol = symbolMappingService.toDisplaySymbol(rawSymbol)
    if (!displaySymbol) {
      console.warn(`⚠️ [MarketDataLookup] No display symbol mapping for TradingView symbol: ${rawSymbol}`)
      // Fallback: try using the raw symbol if no mapping exists
      console.log(`📊 [MarketDataLookup] Using raw symbol as fallback: ${rawSymbol}`)
    }
    
    // Use display symbol for storage (what positions expect) or fallback to raw symbol
    const storageSymbol = displaySymbol || rawSymbol
    
    // TradingView data format conversion
    const price: MarketPrice = {
      symbol: storageSymbol,
      bid: data.bid || data.b || data.price, // TradingView might use different field names
      ask: data.ask || data.a || data.price,
      last: data.last || data.lp || data.price || data.bid,
      spread: (data.ask || data.a || data.price) - (data.bid || data.b || data.price),
      timestamp: data.timestamp || new Date().toISOString()
    }
    
    // Data freshness validation - reject data older than 1 minute for crypto, 5 minutes for forex
    const dataAge = Date.now() - new Date(price.timestamp).getTime()
    const symbolSpecs = symbolMappingService.getSymbolSpecs(storageSymbol)
    const maxAge = symbolSpecs?.category === 'crypto' 
      ? 60 * 1000  // 1 minute for crypto (more volatile)
      : 5 * 60 * 1000 // 5 minutes for forex/commodities/indices
    
    if (dataAge > maxAge) {
      console.warn(`⚠️ [MarketDataLookup] Rejecting stale data for ${storageSymbol}: ${Math.round(dataAge/1000)}s old (max age: ${maxAge/1000}s)`)
      return
    }
    
    // Additional validation for realistic price ranges
    if (storageSymbol === 'XRPUSD' && (price.last > 10.0 || price.last < 0.05)) {
      console.warn(`⚠️ [MarketDataLookup] Rejecting unrealistic XRP price: $${price.last}`)
      return
    }
    
    // CRITICAL FIX: Store with display symbol key (what positions expect)
    this.marketDataStore[storageSymbol] = price
    this.webSocketConnected = true
    
    console.log(`📊 [MarketDataLookup] FIXED MAPPING - TradingView symbol: ${rawSymbol} → Display symbol: ${storageSymbol}`)
    console.log(`📊 [MarketDataLookup] Updated market data: ${storageSymbol} = ${price.bid}/${price.ask} (${Math.round(dataAge/1000)}s old)`)
    console.log(`📊 [MarketDataLookup] Current store keys:`, Object.keys(this.marketDataStore))
    
    // Trigger immediate position updates for this symbol if there are active subscribers
    this.notifySubscribers(storageSymbol, price)
  }

  /**
   * Notify all subscribers of price updates for immediate position recalculation
   */
  private notifySubscribers(symbol: string, price: MarketPrice): void {
    const callbacks = this.priceUpdateCallbacks.get(symbol.toUpperCase())
    if (callbacks && callbacks.length > 0) {
      console.log(`🔔 [MarketDataLookup] Notifying ${callbacks.length} subscribers for ${symbol}`)
      callbacks.forEach(callback => {
        try {
          callback(price)
        } catch (error) {
          console.error(`❌ [MarketDataLookup] Error in price update callback for ${symbol}:`, error)
        }
      })
    }
  }

  /**
   * Subscribe to real-time price updates for a symbol
   * This enables immediate position P&L recalculation when prices change
   */
  public onPriceUpdate(symbol: string, callback: (price: MarketPrice) => void): () => void {
    const normalizedSymbol = symbol.toUpperCase()
    
    if (!this.priceUpdateCallbacks.has(normalizedSymbol)) {
      this.priceUpdateCallbacks.set(normalizedSymbol, [])
    }
    
    this.priceUpdateCallbacks.get(normalizedSymbol)!.push(callback)
    console.log(`📞 [MarketDataLookup] Added price update callback for ${normalizedSymbol}`)
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.priceUpdateCallbacks.get(normalizedSymbol)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
          console.log(`📞 [MarketDataLookup] Removed price update callback for ${normalizedSymbol}`)
          
          // Clean up empty callback arrays
          if (callbacks.length === 0) {
            this.priceUpdateCallbacks.delete(normalizedSymbol)
          }
        }
      }
    }
  }

  /**
   * Set WebSocket connection status
   */
  public setWebSocketStatus(connected: boolean) {
    this.webSocketConnected = connected
  }

  /**
   * Get current market price for a symbol
   */
  public async getMarketPrice(symbol: string): Promise<MarketPrice> {
    const normalizedSymbol = symbol.toUpperCase()
    
    // First try: Live WebSocket data
    if (this.webSocketConnected && this.marketDataStore[normalizedSymbol]) {
      const data = this.marketDataStore[normalizedSymbol]
      console.log(`📊 [MarketDataLookup] Using live data for ${symbol}:`, data)
      return data
    }

    // Second try: Fallback realistic prices
    if (this.fallbackPrices[normalizedSymbol]) {
      const data = this.fallbackPrices[normalizedSymbol]
      console.log(`📊 [MarketDataLookup] Using fallback data for ${symbol}:`, data)
      return data
    }

    // Third try: Database lookup
    try {
      const { data: dbData, error } = await supabase
        .from('market_data')
        .select('*')
        .eq('symbol', normalizedSymbol)
        .order('timestamp', { ascending: false })
        .limit(1)
        .single()

      if (!error && dbData) {
        const price: MarketPrice = {
          symbol: dbData.symbol,
          bid: dbData.bid,
          ask: dbData.ask,
          last: dbData.last || dbData.bid,
          spread: dbData.ask - dbData.bid,
          timestamp: dbData.timestamp
        }
        console.log(`📊 [MarketDataLookup] Using database data for ${symbol}:`, price)
        return price
      }
    } catch (error) {
      console.error(`📊 [MarketDataLookup] Database lookup failed for ${symbol}:`, error)
    }

    // Last resort: Generate synthetic realistic price
    const syntheticPrice = this.generateSyntheticPrice(normalizedSymbol)
    console.log(`📊 [MarketDataLookup] Using synthetic data for ${symbol}:`, syntheticPrice)
    return syntheticPrice
  }

  /**
   * Generate realistic synthetic prices for unknown symbols
   */
  private generateSyntheticPrice(symbol: string): MarketPrice {
    // Base price depending on symbol type
    let basePrice = 1.0000
    let spread = 0.0002

    if (symbol.includes('JPY')) {
      basePrice = 149.86
      spread = 0.02
    } else if (symbol.includes('USD')) {
      basePrice = 1.1000 + (Math.random() - 0.5) * 0.2
      spread = 0.0002
    } else if (symbol.includes('GOLD') || symbol.includes('XAU')) {
      basePrice = 2034.60
      spread = 0.20
    } else if (symbol.includes('OIL') || symbol.includes('XBR') || symbol.includes('WTI')) {
      basePrice = 73.46
      spread = 0.02
    } else if (symbol.includes('BTC')) {
      basePrice = 42851.0
      spread = 1.0
    } else if (symbol.includes('ETH')) {
      basePrice = 2457.0
      spread = 0.40
    }

    // Add some realistic market movement
    const variation = (Math.random() - 0.5) * 0.002 // ±0.1% variation
    const mid = basePrice * (1 + variation)
    const halfSpread = spread / 2

    return {
      symbol,
      bid: parseFloat((mid - halfSpread).toFixed(5)),
      ask: parseFloat((mid + halfSpread).toFixed(5)),
      last: parseFloat(mid.toFixed(5)),
      spread: parseFloat(spread.toFixed(5)),
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Get execution price for an order (buy uses ask, sell uses bid)
   */
  public async getExecutionPrice(symbol: string, orderType: 'buy' | 'sell'): Promise<number> {
    const marketPrice = await this.getMarketPrice(symbol)
    return orderType === 'buy' ? marketPrice.ask : marketPrice.bid
  }

  /**
   * Get current price for P&L calculations (buy positions use bid, sell positions use ask)
   */
  public async getCurrentPrice(symbol: string, positionType: 'buy' | 'sell'): Promise<number> {
    const marketPrice = await this.getMarketPrice(symbol)
    // For P&L: long positions (buy) are marked to bid, short positions (sell) are marked to ask
    return positionType === 'buy' ? marketPrice.bid : marketPrice.ask
  }

  /**
   * Calculate unrealized P&L for a position
   */
  public async calculateUnrealizedPL(
    symbol: string,
    entryPrice: number,
    volume: number,
    positionType: 'buy' | 'sell'
  ): Promise<number> {
    const currentPrice = await this.getCurrentPrice(symbol, positionType)
    
    // P&L calculation: (current - entry) * volume * direction * pip value
    const priceDiff = currentPrice - entryPrice
    const direction = positionType === 'buy' ? 1 : -1
    
    // For forex pairs, multiply by 100,000 (standard lot size)
    // For crypto, use the actual volume
    let pipMultiplier = 100000 // Standard forex lot
    if (symbol.includes('BTC') || symbol.includes('ETH') || symbol.includes('XRP')) {
      pipMultiplier = 1 // Crypto uses actual volume
    }
    
    const unrealizedPL = priceDiff * volume * direction * pipMultiplier

    console.log(`📊 [P&L] ${symbol} ${positionType}: entry=${entryPrice}, current=${currentPrice}, volume=${volume}, pipMultiplier=${pipMultiplier}, P&L=$${unrealizedPL.toFixed(2)}`)
    
    return parseFloat(unrealizedPL.toFixed(2))
  }

  /**
   * Get all current market data
   */
  public getAllMarketData(): MarketDataStore {
    if (this.webSocketConnected && Object.keys(this.marketDataStore).length > 0) {
      return { ...this.marketDataStore }
    }
    return { ...this.fallbackPrices }
  }

  /**
   * Check if we have real-time data for a symbol
   */
  public hasLiveData(symbol: string): boolean {
    return this.webSocketConnected && !!this.marketDataStore[symbol.toUpperCase()]
  }

  /**
   * Subscribe to live data for a symbol with a specific subscriber ID
   */
  public subscribeToSymbol(symbol: string, subscriberId: string): void {
    const normalizedSymbol = symbol.toUpperCase()
    
    if (!this.symbolSubscribers.has(normalizedSymbol)) {
      this.symbolSubscribers.set(normalizedSymbol, new Set())
    }
    
    this.symbolSubscribers.get(normalizedSymbol)!.add(subscriberId)
    this.activeSymbols.add(normalizedSymbol)
    
    console.log(`📊 [MarketDataLookup] ${subscriberId} subscribed to ${normalizedSymbol}. Active symbols:`, Array.from(this.activeSymbols))
  }

  /**
   * Unsubscribe from live data for a symbol with a specific subscriber ID
   */
  public unsubscribeFromSymbol(symbol: string, subscriberId: string): void {
    const normalizedSymbol = symbol.toUpperCase()
    
    if (this.symbolSubscribers.has(normalizedSymbol)) {
      const subscribers = this.symbolSubscribers.get(normalizedSymbol)!
      subscribers.delete(subscriberId)
      
      if (subscribers.size === 0) {
        this.symbolSubscribers.delete(normalizedSymbol)
        this.activeSymbols.delete(normalizedSymbol)
        console.log(`📊 [MarketDataLookup] Removed ${normalizedSymbol} from active symbols`)
      }
    }
    
    console.log(`📊 [MarketDataLookup] ${subscriberId} unsubscribed from ${normalizedSymbol}. Active symbols:`, Array.from(this.activeSymbols))
  }

  /**
   * Get all symbols that need live data feeds
   */
  public getActiveSymbols(): string[] {
    return Array.from(this.activeSymbols)
  }

  /**
   * Check if symbol data is fresh (within last 30 seconds)
   */
  public isDataFresh(symbol: string): boolean {
    const normalizedSymbol = symbol.toUpperCase()
    const data = this.marketDataStore[normalizedSymbol]
    
    if (!data || !data.timestamp) return false
    
    const dataTime = new Date(data.timestamp).getTime()
    const now = Date.now()
    const maxAge = 30 * 1000 // 30 seconds
    
    return (now - dataTime) < maxAge
  }

  /**
   * Add validation for trade execution - ensures we have fresh data
   */
  public validateDataForTrading(symbol: string): { isValid: boolean; reason?: string; data?: MarketPrice } {
    const normalizedSymbol = symbol.toUpperCase()
    
    console.log(`🔍 [MarketDataLookup] Validating data for ${normalizedSymbol}:`, {
      webSocketConnected: this.webSocketConnected,
      hasDataInStore: !!this.marketDataStore[normalizedSymbol],
      storeKeys: Object.keys(this.marketDataStore),
      requestedSymbol: normalizedSymbol
    })
    
    // Check if we have live data
    if (this.webSocketConnected && this.marketDataStore[normalizedSymbol]) {
      const data = this.marketDataStore[normalizedSymbol]
      
      // Check if data is fresh
      if (this.isDataFresh(normalizedSymbol)) {
        console.log(`✅ [MarketDataLookup] Fresh live data found for ${normalizedSymbol}`)
        return { isValid: true, data }
      } else {
        console.log(`⚠️ [MarketDataLookup] Stale data for ${normalizedSymbol}`)
        return { 
          isValid: false, 
          reason: `Live data for ${symbol} is stale (${data.timestamp})`,
          data 
        }
      }
    }
    
    // No live data available
    console.log(`❌ [MarketDataLookup] No live data for ${normalizedSymbol}`)
    return { 
      isValid: false, 
      reason: `No live data available for ${symbol}. Using fallback prices.` 
    }
  }
}

// Export singleton instance
export const marketDataLookup = MarketDataLookupService.getInstance()
export default marketDataLookup