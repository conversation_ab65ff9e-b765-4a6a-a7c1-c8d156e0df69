/**
 * Multi-Account Risk Management Service
 * Manages risk across multiple trading accounts with portfolio-wide controls
 */

import { supabase } from '@/lib/supabase/client'
import type { TradingAccount, Trade } from '@/types/database'

interface RiskLimits {
  maxDrawdownPerAccount: number // Maximum drawdown percentage per account
  maxPortfolioDrawdown: number // Maximum total portfolio drawdown
  maxRiskPerTrade: number // Maximum risk per trade as % of account balance
  maxDailyLoss: number // Maximum daily loss in USD
  maxCorrelatedExposure: number // Maximum exposure to correlated positions
  marginCallLevel: number // Margin level that triggers warnings
  stopOutLevel: number // Margin level that triggers automatic closure
}

interface AccountRiskMetrics {
  accountId: string
  currentDrawdown: number
  marginLevel: number
  dailyPnL: number
  openPositions: number
  totalExposure: number
  riskScore: number
  correlatedPositions: string[]
  lastRiskCheck: string
}

interface RiskAlert {
  id: string
  accountId: string
  type: 'drawdown' | 'margin' | 'daily_loss' | 'correlation' | 'position_size'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  value: number
  threshold: number
  triggered_at: string
  resolved: boolean
}

interface RiskAction {
  type: 'close_position' | 'reduce_position' | 'block_new_trades' | 'margin_call' | 'notify_user'
  accountId: string
  positionId?: string
  reason: string
  executedAt: string
}

export class MultiAccountRiskService {
  private defaultRiskLimits: RiskLimits = {
    maxDrawdownPerAccount: 15, // 15% max drawdown per account
    maxPortfolioDrawdown: 12, // 12% max portfolio drawdown
    maxRiskPerTrade: 3, // 3% max risk per trade
    maxDailyLoss: 2000, // $2000 max daily loss across portfolio
    maxCorrelatedExposure: 25, // 25% max exposure to correlated positions
    marginCallLevel: 200, // 200% margin level warning
    stopOutLevel: 50, // 50% margin level auto-close
  }

  /**
   * Perform comprehensive risk assessment across all accounts
   */
  async assessPortfolioRisk(userId: string): Promise<{
    accountMetrics: AccountRiskMetrics[]
    portfolioRisk: number
    activeAlerts: RiskAlert[]
    recommendedActions: RiskAction[]
  }> {
    try {
      // Get all user accounts
      const { data: accounts, error: accountsError } = await supabase
        .from('trading_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')

      if (accountsError || !accounts) {
        throw new Error('Failed to fetch accounts')
      }

      // Get risk limits for user (or use defaults)
      const riskLimits = await this.getUserRiskLimits(userId)

      // Calculate metrics for each account
      const accountMetrics: AccountRiskMetrics[] = []
      for (const account of accounts) {
        const metrics = await this.calculateAccountRiskMetrics(account, riskLimits)
        accountMetrics.push(metrics)
      }

      // Calculate portfolio-wide risk
      const portfolioRisk = this.calculatePortfolioRisk(accountMetrics, accounts)

      // Check for risk violations and generate alerts
      const alerts = await this.generateRiskAlerts(accountMetrics, riskLimits, userId)

      // Generate recommended actions
      const recommendedActions = this.generateRiskActions(accountMetrics, alerts, riskLimits)

      return {
        accountMetrics,
        portfolioRisk,
        activeAlerts: alerts,
        recommendedActions
      }
    } catch (error) {
      console.error('Error assessing portfolio risk:', error)
      throw error
    }
  }

  /**
   * Calculate risk metrics for individual account
   */
  private async calculateAccountRiskMetrics(
    account: TradingAccount, 
    riskLimits: RiskLimits
  ): Promise<AccountRiskMetrics> {
    // Get recent trades
    const { data: trades } = await supabase
      .from('trades')
      .select('*')
      .eq('account_id', account.id)
      .order('created_at', { ascending: false })
      .limit(100)

    const recentTrades = trades || []
    const openPositions = recentTrades.filter(t => t.status === 'open')

    // Calculate daily P&L
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)
    
    const todayTrades = recentTrades.filter(t => 
      t.close_time && new Date(t.close_time) >= todayStart
    )
    const dailyPnL = todayTrades.reduce((sum, t) => sum + (t.profit || 0), 0)

    // Calculate total exposure
    const totalExposure = openPositions.reduce((sum, t) => 
      sum + (t.volume * t.open_price), 0
    )

    // Find correlated positions (same symbol or sector)
    const correlatedPositions = this.findCorrelatedPositions(openPositions)

    // Calculate risk score
    const riskScore = this.calculateRiskScore(account, recentTrades, riskLimits)

    return {
      accountId: account.id,
      currentDrawdown: account.drawdown,
      marginLevel: account.margin_level,
      dailyPnL,
      openPositions: openPositions.length,
      totalExposure,
      riskScore,
      correlatedPositions,
      lastRiskCheck: new Date().toISOString()
    }
  }

  /**
   * Calculate portfolio-wide risk score
   */
  private calculatePortfolioRisk(
    accountMetrics: AccountRiskMetrics[], 
    accounts: TradingAccount[]
  ): number {
    if (accountMetrics.length === 0) return 0

    const totalBalance = accounts.reduce((sum, acc) => sum + acc.balance, 0)
    const weightedRisk = accountMetrics.reduce((sum, metrics) => {
      const account = accounts.find(acc => acc.id === metrics.accountId)
      const weight = account ? account.balance / totalBalance : 0
      return sum + (metrics.riskScore * weight)
    }, 0)

    // Adjust for portfolio concentration
    const maxAccountWeight = Math.max(...accounts.map(acc => acc.balance / totalBalance))
    const concentrationPenalty = maxAccountWeight > 0.5 ? (maxAccountWeight - 0.5) * 20 : 0

    // Adjust for correlation risk
    const totalCorrelatedPositions = accountMetrics.reduce((sum, m) => 
      sum + m.correlatedPositions.length, 0
    )
    const correlationPenalty = totalCorrelatedPositions > 5 ? (totalCorrelatedPositions - 5) * 2 : 0

    return Math.min(100, weightedRisk + concentrationPenalty + correlationPenalty)
  }

  /**
   * Generate risk alerts based on violations
   */
  private async generateRiskAlerts(
    accountMetrics: AccountRiskMetrics[], 
    riskLimits: RiskLimits,
    userId: string
  ): Promise<RiskAlert[]> {
    const alerts: RiskAlert[] = []

    for (const metrics of accountMetrics) {
      // Drawdown alerts
      if (metrics.currentDrawdown > riskLimits.maxDrawdownPerAccount) {
        alerts.push({
          id: `drawdown_${metrics.accountId}_${Date.now()}`,
          accountId: metrics.accountId,
          type: 'drawdown',
          severity: metrics.currentDrawdown > riskLimits.maxDrawdownPerAccount * 1.2 ? 'critical' : 'high',
          message: `Account drawdown ${metrics.currentDrawdown.toFixed(2)}% exceeds limit of ${riskLimits.maxDrawdownPerAccount}%`,
          value: metrics.currentDrawdown,
          threshold: riskLimits.maxDrawdownPerAccount,
          triggered_at: new Date().toISOString(),
          resolved: false
        })
      }

      // Margin level alerts
      if (metrics.marginLevel < riskLimits.marginCallLevel) {
        alerts.push({
          id: `margin_${metrics.accountId}_${Date.now()}`,
          accountId: metrics.accountId,
          type: 'margin',
          severity: metrics.marginLevel < riskLimits.stopOutLevel ? 'critical' : 
                   metrics.marginLevel < riskLimits.marginCallLevel * 0.75 ? 'high' : 'medium',
          message: `Margin level ${metrics.marginLevel.toFixed(0)}% below threshold of ${riskLimits.marginCallLevel}%`,
          value: metrics.marginLevel,
          threshold: riskLimits.marginCallLevel,
          triggered_at: new Date().toISOString(),
          resolved: false
        })
      }

      // Daily loss alerts
      if (metrics.dailyPnL < -riskLimits.maxDailyLoss) {
        alerts.push({
          id: `daily_loss_${metrics.accountId}_${Date.now()}`,
          accountId: metrics.accountId,
          type: 'daily_loss',
          severity: Math.abs(metrics.dailyPnL) > riskLimits.maxDailyLoss * 1.5 ? 'critical' : 'high',
          message: `Daily loss $${Math.abs(metrics.dailyPnL).toFixed(2)} exceeds limit of $${riskLimits.maxDailyLoss}`,
          value: Math.abs(metrics.dailyPnL),
          threshold: riskLimits.maxDailyLoss,
          triggered_at: new Date().toISOString(),
          resolved: false
        })
      }

      // Correlation alerts
      if (metrics.correlatedPositions.length > 3) {
        alerts.push({
          id: `correlation_${metrics.accountId}_${Date.now()}`,
          accountId: metrics.accountId,
          type: 'correlation',
          severity: metrics.correlatedPositions.length > 5 ? 'high' : 'medium',
          message: `${metrics.correlatedPositions.length} correlated positions detected, increasing portfolio risk`,
          value: metrics.correlatedPositions.length,
          threshold: 3,
          triggered_at: new Date().toISOString(),
          resolved: false
        })
      }
    }

    // Store alerts in database
    if (alerts.length > 0) {
      await this.storeRiskAlerts(alerts, userId)
    }

    return alerts
  }

  /**
   * Generate recommended risk management actions
   */
  private generateRiskActions(
    accountMetrics: AccountRiskMetrics[], 
    alerts: RiskAlert[], 
    riskLimits: RiskLimits
  ): RiskAction[] {
    const actions: RiskAction[] = []

    for (const alert of alerts) {
      const metrics = accountMetrics.find(m => m.accountId === alert.accountId)
      if (!metrics) continue

      switch (alert.type) {
        case 'drawdown':
          if (alert.severity === 'critical') {
            actions.push({
              type: 'close_position',
              accountId: alert.accountId,
              reason: 'Critical drawdown level reached',
              executedAt: new Date().toISOString()
            })
          } else {
            actions.push({
              type: 'block_new_trades',
              accountId: alert.accountId,
              reason: 'Maximum drawdown exceeded',
              executedAt: new Date().toISOString()
            })
          }
          break

        case 'margin':
          if (alert.severity === 'critical') {
            actions.push({
              type: 'close_position',
              accountId: alert.accountId,
              reason: 'Margin level critically low',
              executedAt: new Date().toISOString()
            })
          } else {
            actions.push({
              type: 'margin_call',
              accountId: alert.accountId,
              reason: 'Margin level below threshold',
              executedAt: new Date().toISOString()
            })
          }
          break

        case 'daily_loss':
          actions.push({
            type: 'block_new_trades',
            accountId: alert.accountId,
            reason: 'Daily loss limit exceeded',
            executedAt: new Date().toISOString()
          })
          break

        case 'correlation':
          actions.push({
            type: 'reduce_position',
            accountId: alert.accountId,
            reason: 'Reduce correlated position exposure',
            executedAt: new Date().toISOString()
          })
          break

        default:
          actions.push({
            type: 'notify_user',
            accountId: alert.accountId,
            reason: alert.message,
            executedAt: new Date().toISOString()
          })
      }
    }

    return actions
  }

  /**
   * Execute automated risk management actions
   */
  async executeRiskActions(
    actions: RiskAction[], 
    userId: string
  ): Promise<{ executed: RiskAction[], failed: RiskAction[] }> {
    const executed: RiskAction[] = []
    const failed: RiskAction[] = []

    for (const action of actions) {
      try {
        switch (action.type) {
          case 'close_position':
            await this.closeRiskyPositions(action.accountId)
            executed.push(action)
            break

          case 'reduce_position':
            await this.reducePositionSizes(action.accountId)
            executed.push(action)
            break

          case 'block_new_trades':
            await this.blockNewTrades(action.accountId)
            executed.push(action)
            break

          case 'margin_call':
            await this.issueMarginCall(action.accountId, userId)
            executed.push(action)
            break

          case 'notify_user':
            await this.notifyUser(userId, action.reason)
            executed.push(action)
            break

          default:
            failed.push(action)
        }
      } catch (error) {
        console.error(`Failed to execute risk action:`, error)
        failed.push(action)
      }
    }

    // Log executed actions
    if (executed.length > 0) {
      await this.logRiskActions(executed, userId)
    }

    return { executed, failed }
  }

  /**
   * Calculate risk score for an account
   */
  private calculateRiskScore(
    account: TradingAccount, 
    recentTrades: Trade[], 
    riskLimits: RiskLimits
  ): number {
    let score = 0

    // Drawdown component (0-40 points)
    const drawdownRatio = account.drawdown / riskLimits.maxDrawdownPerAccount
    score += Math.min(40, drawdownRatio * 40)

    // Margin level component (0-30 points)
    const marginRisk = Math.max(0, (riskLimits.marginCallLevel - account.margin_level) / riskLimits.marginCallLevel)
    score += marginRisk * 30

    // Trading performance component (0-20 points)
    const closedTrades = recentTrades.filter(t => t.status === 'closed')
    if (closedTrades.length > 0) {
      const winRate = closedTrades.filter(t => (t.profit || 0) > 0).length / closedTrades.length
      if (winRate < 0.4) score += 20
      else if (winRate < 0.5) score += 10
    }

    // Position concentration component (0-10 points)
    const openPositions = recentTrades.filter(t => t.status === 'open')
    if (openPositions.length > 5) {
      score += Math.min(10, (openPositions.length - 5) * 2)
    }

    return Math.min(100, score)
  }

  /**
   * Find correlated positions across accounts
   */
  private findCorrelatedPositions(positions: Trade[]): string[] {
    const symbols = positions.map(p => p.symbol)
    const uniqueSymbols = [...new Set(symbols)]
    
    // Simple correlation: same symbol = correlated
    return uniqueSymbols.filter(symbol => 
      positions.filter(p => p.symbol === symbol).length > 1
    )
  }

  /**
   * Get user-specific risk limits
   */
  private async getUserRiskLimits(userId: string): Promise<RiskLimits> {
    // In a real implementation, this would fetch from a user_risk_settings table
    // For now, return defaults
    return this.defaultRiskLimits
  }

  /**
   * Store risk alerts in database
   */
  private async storeRiskAlerts(alerts: RiskAlert[], userId: string): Promise<void> {
    try {
      await supabase
        .from('risk_alerts')
        .insert(alerts.map(alert => ({
          ...alert,
          user_id: userId,
          created_at: new Date().toISOString()
        })))
    } catch (error) {
      console.error('Failed to store risk alerts:', error)
    }
  }

  /**
   * Log risk actions in database
   */
  private async logRiskActions(actions: RiskAction[], userId: string): Promise<void> {
    try {
      await supabase
        .from('risk_actions')
        .insert(actions.map(action => ({
          ...action,
          user_id: userId,
          created_at: new Date().toISOString()
        })))
    } catch (error) {
      console.error('Failed to log risk actions:', error)
    }
  }

  /**
   * Close risky positions
   */
  private async closeRiskyPositions(accountId: string): Promise<void> {
    const { data: openTrades } = await supabase
      .from('trades')
      .select('*')
      .eq('account_id', accountId)
      .eq('status', 'open')

    if (openTrades) {
      // Close largest losing positions first
      const losingTrades = openTrades
        .filter(t => (t.profit || 0) < 0)
        .sort((a, b) => (a.profit || 0) - (b.profit || 0))
        .slice(0, 3) // Close up to 3 worst positions

      for (const trade of losingTrades) {
        await supabase
          .from('trades')
          .update({
            status: 'closed',
            close_time: new Date().toISOString(),
            close_price: trade.open_price, // Simplified - would use current market price
            comment: 'Closed by risk management system'
          })
          .eq('id', trade.id)
      }
    }
  }

  /**
   * Reduce position sizes
   */
  private async reducePositionSizes(accountId: string): Promise<void> {
    const { data: openTrades } = await supabase
      .from('trades')
      .select('*')
      .eq('account_id', accountId)
      .eq('status', 'open')

    if (openTrades) {
      // Reduce volume of largest positions by 50%
      const largeTrades = openTrades
        .sort((a, b) => (b.volume * b.open_price) - (a.volume * a.open_price))
        .slice(0, 2)

      for (const trade of largeTrades) {
        await supabase
          .from('trades')
          .update({
            volume: trade.volume * 0.5,
            comment: 'Position reduced by risk management'
          })
          .eq('id', trade.id)
      }
    }
  }

  /**
   * Block new trades for account
   */
  private async blockNewTrades(accountId: string): Promise<void> {
    await supabase
      .from('trading_accounts')
      .update({
        trading_enabled: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', accountId)
  }

  /**
   * Issue margin call notification
   */
  private async issueMarginCall(accountId: string, userId: string): Promise<void> {
    // Create margin call record
    await supabase
      .from('margin_calls')
      .insert({
        account_id: accountId,
        user_id: userId,
        call_time: new Date().toISOString(),
        status: 'active'
      })
  }

  /**
   * Notify user of risk events
   */
  private async notifyUser(userId: string, message: string): Promise<void> {
    // Create notification record
    await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: 'risk_alert',
        message,
        created_at: new Date().toISOString(),
        read: false
      })
  }
}

// Export singleton instance
export const multiAccountRiskService = new MultiAccountRiskService()