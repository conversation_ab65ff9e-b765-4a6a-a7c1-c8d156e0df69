/**
 * Symbol Mapping Service
 * Provides bidirectional symbol conversion between display symbols and TradingView normalized symbols
 * Critical for ensuring data consistency between chart data and position calculations
 */

export interface SymbolMapping {
  displaySymbol: string
  tradingViewSymbol: string
  category: 'forex' | 'commodity' | 'index' | 'crypto'
  pipValue: number
  lotSize: number
}

/**
 * Comprehensive symbol mapping table
 * Maps display symbols (used in positions) to TradingView normalized symbols (used in chart data)
 */
const SYMBOL_MAPPINGS: SymbolMapping[] = [
  // Forex pairs
  { displaySymbol: 'EUR/USD', tradingViewSymbol: 'FX_IDC:EURUSD', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  { displaySymbol: 'GBP/USD', tradingViewSymbol: 'FX_IDC:GBPUSD', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  { displaySymbol: 'USD/JPY', tradingViewSymbol: 'FX_IDC:USDJPY', category: 'forex', pipValue: 0.01, lotSize: 100000 },
  { displaySymbol: 'USD/CHF', tradingViewSymbol: 'FX_IDC:USDCHF', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  { displaySymbol: 'AUD/USD', tradingViewSymbol: 'FX_IDC:AUDUSD', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  { displaySymbol: 'USD/CAD', tradingViewSymbol: 'FX_IDC:USDCAD', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  { displaySymbol: 'NZD/USD', tradingViewSymbol: 'FX_IDC:NZDUSD', category: 'forex', pipValue: 0.0001, lotSize: 100000 },
  
  // Commodities
  { displaySymbol: 'GOLD', tradingViewSymbol: 'TVC:GOLD', category: 'commodity', pipValue: 0.01, lotSize: 100 },
  { displaySymbol: 'SILVER', tradingViewSymbol: 'TVC:SILVER', category: 'commodity', pipValue: 0.001, lotSize: 5000 },
  { displaySymbol: 'OIL', tradingViewSymbol: 'TVC:USOIL', category: 'commodity', pipValue: 0.01, lotSize: 1000 },
  { displaySymbol: 'XBRUSD', tradingViewSymbol: 'TVC:UKOIL', category: 'commodity', pipValue: 0.01, lotSize: 1000 },
  { displaySymbol: 'NATGAS', tradingViewSymbol: 'TVC:NATURALGAS', category: 'commodity', pipValue: 0.001, lotSize: 10000 },
  
  // Indices
  { displaySymbol: 'SPX500', tradingViewSymbol: 'SP:SPX', category: 'index', pipValue: 0.01, lotSize: 1 },
  { displaySymbol: 'US30', tradingViewSymbol: 'TVC:DJI', category: 'index', pipValue: 1, lotSize: 1 },
  { displaySymbol: 'NAS100', tradingViewSymbol: 'TVC:IXIC', category: 'index', pipValue: 0.01, lotSize: 1 },
  { displaySymbol: 'UK100', tradingViewSymbol: 'TVC:UKX', category: 'index', pipValue: 0.01, lotSize: 1 },
  
  // Crypto
  { displaySymbol: 'BTCUSD', tradingViewSymbol: 'COINBASE:BTCUSD', category: 'crypto', pipValue: 1, lotSize: 1 },
  { displaySymbol: 'ETHUSD', tradingViewSymbol: 'COINBASE:ETHUSD', category: 'crypto', pipValue: 0.01, lotSize: 1 },
  { displaySymbol: 'XRPUSD', tradingViewSymbol: 'COINBASE:XRPUSD', category: 'crypto', pipValue: 0.0001, lotSize: 1 }
]

class SymbolMappingService {
  private static instance: SymbolMappingService
  private displayToTradingView: Map<string, SymbolMapping> = new Map()
  private tradingViewToDisplay: Map<string, SymbolMapping> = new Map()

  private constructor() {
    this.initializeMappings()
  }

  public static getInstance(): SymbolMappingService {
    if (!SymbolMappingService.instance) {
      SymbolMappingService.instance = new SymbolMappingService()
    }
    return SymbolMappingService.instance
  }

  /**
   * Initialize bidirectional mapping tables
   */
  private initializeMappings(): void {
    SYMBOL_MAPPINGS.forEach(mapping => {
      // Normalize symbols to uppercase for consistent lookups
      const displayKey = mapping.displaySymbol.toUpperCase()
      const tradingViewKey = mapping.tradingViewSymbol.toUpperCase()
      
      this.displayToTradingView.set(displayKey, mapping)
      this.tradingViewToDisplay.set(tradingViewKey, mapping)
    })

    console.log(`📊 [SymbolMappingService] Initialized ${SYMBOL_MAPPINGS.length} symbol mappings`)
    console.log(`📊 [SymbolMappingService] Display symbols:`, Array.from(this.displayToTradingView.keys()))
    console.log(`📊 [SymbolMappingService] TradingView symbols:`, Array.from(this.tradingViewToDisplay.keys()))
  }

  /**
   * Convert display symbol to TradingView normalized symbol
   * @param displaySymbol - Symbol in display format (e.g., 'EUR/USD')
   * @returns TradingView normalized symbol (e.g., 'FX_IDC:EURUSD') or null if not found
   */
  public toTradingViewSymbol(displaySymbol: string): string | null {
    const normalizedSymbol = displaySymbol.toUpperCase()
    const mapping = this.displayToTradingView.get(normalizedSymbol)
    
    if (!mapping) {
      console.warn(`⚠️ [SymbolMappingService] No TradingView mapping found for display symbol: ${displaySymbol}`)
      return null
    }
    
    console.log(`🔄 [SymbolMappingService] Display → TradingView: ${displaySymbol} → ${mapping.tradingViewSymbol}`)
    return mapping.tradingViewSymbol
  }

  /**
   * Convert TradingView normalized symbol to display symbol
   * @param tradingViewSymbol - Symbol in TradingView format (e.g., 'FX_IDC:EURUSD')
   * @returns Display symbol (e.g., 'EUR/USD') or null if not found
   */
  public toDisplaySymbol(tradingViewSymbol: string): string | null {
    const normalizedSymbol = tradingViewSymbol.toUpperCase()
    const mapping = this.tradingViewToDisplay.get(normalizedSymbol)
    
    if (!mapping) {
      console.warn(`⚠️ [SymbolMappingService] No display mapping found for TradingView symbol: ${tradingViewSymbol}`)
      return null
    }
    
    console.log(`🔄 [SymbolMappingService] TradingView → Display: ${tradingViewSymbol} → ${mapping.displaySymbol}`)
    return mapping.displaySymbol
  }

  /**
   * Get complete symbol mapping information
   * @param symbol - Symbol in either display or TradingView format
   * @returns Complete mapping information or null if not found
   */
  public getSymbolMapping(symbol: string): SymbolMapping | null {
    const normalizedSymbol = symbol.toUpperCase()
    
    // Try display symbol first
    let mapping = this.displayToTradingView.get(normalizedSymbol)
    if (mapping) return mapping
    
    // Try TradingView symbol
    mapping = this.tradingViewToDisplay.get(normalizedSymbol)
    if (mapping) return mapping
    
    console.warn(`⚠️ [SymbolMappingService] No mapping found for symbol: ${symbol}`)
    return null
  }

  /**
   * Validate if a symbol has a mapping
   * @param symbol - Symbol in either format
   * @returns true if mapping exists, false otherwise
   */
  public hasMapping(symbol: string): boolean {
    return this.getSymbolMapping(symbol) !== null
  }

  /**
   * Get all supported display symbols
   * @returns Array of all display symbols
   */
  public getAllDisplaySymbols(): string[] {
    return Array.from(this.displayToTradingView.keys())
  }

  /**
   * Get all supported TradingView symbols
   * @returns Array of all TradingView symbols
   */
  public getAllTradingViewSymbols(): string[] {
    return Array.from(this.tradingViewToDisplay.keys())
  }

  /**
   * Get symbols by category
   * @param category - Symbol category to filter by
   * @returns Array of display symbols in the specified category
   */
  public getSymbolsByCategory(category: 'forex' | 'commodity' | 'index' | 'crypto'): string[] {
    return SYMBOL_MAPPINGS
      .filter(mapping => mapping.category === category)
      .map(mapping => mapping.displaySymbol)
  }

  /**
   * Get pip value and lot size for a symbol
   * @param symbol - Symbol in either format
   * @returns Object with pipValue and lotSize, or null if not found
   */
  public getSymbolSpecs(symbol: string): { pipValue: number; lotSize: number; category: string } | null {
    const mapping = this.getSymbolMapping(symbol)
    if (!mapping) return null
    
    return {
      pipValue: mapping.pipValue,
      lotSize: mapping.lotSize,
      category: mapping.category
    }
  }

  /**
   * Normalize symbol to ensure consistent formatting
   * Useful for API calls and data storage
   * @param symbol - Symbol in any format
   * @param targetFormat - Target format ('display' or 'tradingview')
   * @returns Normalized symbol or original if no mapping found
   */
  public normalizeSymbol(symbol: string, targetFormat: 'display' | 'tradingview'): string {
    if (targetFormat === 'display') {
      const displaySymbol = this.toDisplaySymbol(symbol)
      return displaySymbol || symbol
    } else {
      const tradingViewSymbol = this.toTradingViewSymbol(symbol)
      return tradingViewSymbol || symbol
    }
  }

  /**
   * Bulk convert multiple symbols
   * @param symbols - Array of symbols to convert
   * @param targetFormat - Target format for conversion
   * @returns Array of converted symbols (preserves order, null for unmapped symbols)
   */
  public convertSymbols(symbols: string[], targetFormat: 'display' | 'tradingview'): (string | null)[] {
    return symbols.map(symbol => {
      if (targetFormat === 'display') {
        return this.toDisplaySymbol(symbol)
      } else {
        return this.toTradingViewSymbol(symbol)
      }
    })
  }

  /**
   * Validate symbol mapping consistency
   * Used for debugging and testing
   * @returns Validation report
   */
  public validateMappings(): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // Check for bidirectional mapping consistency
    for (const [displaySymbol, mapping] of this.displayToTradingView) {
      const backMapping = this.tradingViewToDisplay.get(mapping.tradingViewSymbol.toUpperCase())
      if (!backMapping || backMapping.displaySymbol.toUpperCase() !== displaySymbol) {
        errors.push(`Inconsistent bidirectional mapping for ${displaySymbol}`)
      }
    }
    
    // Check for duplicate mappings
    const tradingViewSymbols = Array.from(this.tradingViewToDisplay.keys())
    const duplicates = tradingViewSymbols.filter((symbol, index) => tradingViewSymbols.indexOf(symbol) !== index)
    if (duplicates.length > 0) {
      errors.push(`Duplicate TradingView symbols: ${duplicates.join(', ')}`)
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// Export singleton instance
export const symbolMappingService = SymbolMappingService.getInstance()
export default symbolMappingService