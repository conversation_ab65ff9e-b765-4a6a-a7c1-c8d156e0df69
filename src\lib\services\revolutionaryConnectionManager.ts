'use client'

/**
 * 🚀 Revolutionary Service Connection Manager
 * 
 * Singleton connection manager to prevent duplicate WebSocket connections
 * Ensures only one connection per symbol across all components
 */

import { io, Socket } from 'socket.io-client'

interface MarketData {
  symbol: string
  data: {
    lp?: number  // Last price
    bid?: number
    ask?: number
    ch?: number  // Change
    chp?: number // Change percent
    volume?: number
  }
  timestamp: string
  source: string
  connectionId: string
}

interface SubscriptionCallback {
  id: string
  callback: (data: MarketData) => void
}

class RevolutionaryConnectionManager {
  private static instance: RevolutionaryConnectionManager | null = null
  private socket: Socket | null = null
  private subscriptions: Map<string, SubscriptionCallback[]> = new Map()
  private connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error' = 'disconnected'
  private connectionStats = {
    connectedAt: null as Date | null,
    reconnectCount: 0,
    messageCount: 0,
    totalSubscriptions: 0,
    maxConnections: 1 // Enforce single connection
  }
  private connectionLock = false

  private constructor(private serviceUrl: string) {}

  static getInstance(serviceUrl: string = 'http://localhost:3002'): RevolutionaryConnectionManager {
    if (!RevolutionaryConnectionManager.instance) {
      console.log('🚀 Creating new RevolutionaryConnectionManager singleton instance')
      RevolutionaryConnectionManager.instance = new RevolutionaryConnectionManager(serviceUrl)
    } else {
      console.log('📊 Reusing existing RevolutionaryConnectionManager singleton instance')
    }
    return RevolutionaryConnectionManager.instance
  }

  getConnectionStatus() {
    return this.connectionStatus
  }

  getConnectionStats() {
    return { ...this.connectionStats }
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Prevent multiple simultaneous connection attempts
      if (this.connectionLock) {
        console.log('🔒 Connection attempt blocked - another connection in progress')
        // Instead of rejecting, wait for current connection to complete
        const checkConnection = () => {
          if (!this.connectionLock && this.socket?.connected) {
            resolve()
          } else if (!this.connectionLock && this.connectionStatus === 'error') {
            reject(new Error('Previous connection failed'))
          } else {
            setTimeout(checkConnection, 100) // Check again in 100ms
          }
        }
        checkConnection()
        return
      }

      if (this.socket?.connected) {
        console.log('🚀 Revolutionary Service already connected')
        resolve()
        return
      }

      console.log(`🚀 Connecting to Revolutionary Service at ${this.serviceUrl}...`)
      this.connectionStatus = 'connecting'
      this.connectionLock = true

      try {
        this.socket = io(this.serviceUrl, {
          transports: ['websocket', 'polling'],
          upgrade: true,
          rememberUpgrade: true,
          reconnection: true,
          reconnectionAttempts: 10,
          reconnectionDelay: 2000,
          timeout: 10000
        })

        this.socket.on('connect', () => {
          console.log('✅ Revolutionary Service connected!')
          this.connectionStatus = 'connected'
          this.connectionStats.connectedAt = new Date()
          this.connectionStats.reconnectCount = this.connectionStats.connectedAt ? this.connectionStats.reconnectCount + 1 : 0
          this.connectionLock = false // Release connection lock

          // Re-subscribe to all existing symbols
          for (const symbol of this.subscriptions.keys()) {
            console.log(`🔄 Re-subscribing to ${symbol}`)
            this.socket!.emit('subscribe', { symbol })
          }

          resolve()
        })

        this.socket.on('disconnect', (reason) => {
          console.log(`🔌 Revolutionary Service disconnected: ${reason}`)
          this.connectionStatus = 'disconnected'
        })

        this.socket.on('connect_error', (error) => {
          console.error('❌ Revolutionary Service connection error:', error)
          this.connectionStatus = 'error'
          this.connectionLock = false // Release connection lock on error
          reject(error)
        })

        this.socket.on('market_data', (data: MarketData) => {
          this.connectionStats.messageCount++
          console.log(`📊 Revolutionary data: ${data.symbol} = ${data.data?.lp || 'N/A'}`)
          
          // Broadcast to all subscribers for this symbol
          const symbolSubscriptions = this.subscriptions.get(data.symbol)
          if (symbolSubscriptions) {
            symbolSubscriptions.forEach(sub => sub.callback(data))
          }
        })

        this.socket.on('subscription_confirmed', (data) => {
          console.log(`✅ Subscription confirmed: ${data.symbol}`)
        })

      } catch (error) {
        console.error('❌ Failed to create Revolutionary Service socket:', error)
        this.connectionStatus = 'error'
        this.connectionLock = false // Release connection lock on error
        reject(error)
      }
    })
  }

  subscribe(symbol: string, callback: (data: MarketData) => void): string {
    const subscriptionId = `${symbol}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Update total subscription count
    this.connectionStats.totalSubscriptions++
    
    console.log(`📊 Adding subscription for ${symbol} (ID: ${subscriptionId}) - Total subscriptions: ${this.connectionStats.totalSubscriptions}`)
    
    // Add to subscriptions map
    if (!this.subscriptions.has(symbol)) {
      this.subscriptions.set(symbol, [])
    }
    this.subscriptions.get(symbol)!.push({ id: subscriptionId, callback })

    // If this is the first subscription for this symbol, send subscribe request
    if (this.subscriptions.get(symbol)!.length === 1) {
      if (this.socket?.connected) {
        console.log(`🚀 Subscribing to ${symbol} (first subscriber)`)
        this.socket.emit('subscribe', { symbol })
      } else {
        console.log(`⏳ Will subscribe to ${symbol} when connected`)
      }
    } else {
      console.log(`📊 Additional subscriber for ${symbol} (${this.subscriptions.get(symbol)!.length} total)`)
    }

    return subscriptionId
  }

  unsubscribe(subscriptionId: string, symbol: string) {
    console.log(`📊 Removing subscription ${subscriptionId} for ${symbol}`)
    
    const symbolSubscriptions = this.subscriptions.get(symbol)
    if (symbolSubscriptions) {
      const index = symbolSubscriptions.findIndex(sub => sub.id === subscriptionId)
      if (index >= 0) {
        symbolSubscriptions.splice(index, 1)
        this.connectionStats.totalSubscriptions--
        
        // If no more subscribers for this symbol, unsubscribe from service
        if (symbolSubscriptions.length === 0) {
          console.log(`🔌 No more subscribers for ${symbol}, unsubscribing from service`)
          this.subscriptions.delete(symbol)
          if (this.socket?.connected) {
            this.socket.emit('unsubscribe', { symbol })
          }
        } else {
          console.log(`📊 ${symbolSubscriptions.length} subscribers remaining for ${symbol}`)
        }
        
        console.log(`📊 Total subscriptions now: ${this.connectionStats.totalSubscriptions}`)
      }
    }
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting from Revolutionary Service...')
      this.socket.disconnect()
      this.socket = null
    }
    this.connectionStatus = 'disconnected'
    this.subscriptions.clear()
  }

  getSubscriptionCount(symbol?: string): number {
    if (symbol) {
      return this.subscriptions.get(symbol)?.length || 0
    }
    return Array.from(this.subscriptions.values()).reduce((total, subs) => total + subs.length, 0)
  }

  getSubscribedSymbols(): string[] {
    return Array.from(this.subscriptions.keys())
  }
}

export default RevolutionaryConnectionManager