'use client'

import { TradingViewCandle, TradingViewQuote } from './tradingViewService'

interface MockDataConfig {
  enableLogging: boolean
  priceVolatility: number // Percentage volatility (0.01 = 1%)
  updateInterval: number // Milliseconds between updates
  enableTrends: boolean // Whether to simulate trending markets
}

/**
 * Mock Trading Data Service
 * 
 * Provides realistic market data simulation when TradingView connections fail.
 * Used as a fallback to ensure trading interface remains functional.
 * 
 * Features:
 * - Realistic price movements with volatility
 * - Historical candle data generation
 * - Live quote updates with callbacks
 * - Trending market simulation
 * - Multi-symbol support
 */
export class MockTradingDataService {
  private config: MockDataConfig
  private lastPrices: Map<string, number> = new Map()
  private quoteCallbacks: Map<string, Function[]> = new Map()
  private candleCallbacks: Map<string, Function[]> = new Map()
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map()
  private trendDirections: Map<string, number> = new Map() // -1, 0, 1 for down, sideways, up

  constructor(config: Partial<MockDataConfig> = {}) {
    this.config = {
      enableLogging: process.env.NODE_ENV === 'development',
      priceVolatility: 0.002, // 0.2% default volatility
      updateInterval: 1000, // 1 second updates
      enableTrends: true,
      ...config
    }

    if (this.config.enableLogging) {
      console.log('📊 [MockTradingDataService] Initialized with config:', this.config)
    }
  }

  /**
   * Get initial price for a symbol
   */
  private getInitialPrice(symbol: string): number {
    const priceMap: { [key: string]: number } = {
      // Forex pairs
      'EUR/USD': 1.0950,
      'GBP/USD': 1.2750,
      'USD/JPY': 149.50,
      'USD/CHF': 0.8850,
      'AUD/USD': 0.6650,
      'USD/CAD': 1.3550,
      'NZD/USD': 0.6150,
      
      // Commodities
      'GOLD': 2650.00,
      'SILVER': 31.50,
      'OIL': 75.50,
      'NATGAS': 3.25,
      
      // Indices
      'SPX500': 4750.00,
      'US30': 37500.00,
      'NAS100': 16500.00,
      'UK100': 7800.00,
      
      // Crypto
      'BTCUSD': 65000.00,
      'ETHUSD': 3200.00,
      'XRPUSD': 0.55
    }

    return priceMap[symbol] || 1.0000
  }

  /**
   * Generate realistic price movement
   */
  private generatePriceMovement(symbol: string, currentPrice: number): number {
    // Base volatility
    let volatility = this.config.priceVolatility
    
    // Adjust volatility by symbol type
    if (symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP')) {
      volatility *= 0.5 // Forex is less volatile
    } else if (symbol === 'BTCUSD' || symbol === 'ETHUSD') {
      volatility *= 3 // Crypto is more volatile
    }

    // Generate random movement
    let movement = (Math.random() - 0.5) * 2 * volatility

    // Apply trend if enabled
    if (this.config.enableTrends) {
      const trend = this.trendDirections.get(symbol) || 0
      
      // Change trend occasionally
      if (Math.random() < 0.01) { // 1% chance to change trend
        this.trendDirections.set(symbol, Math.floor(Math.random() * 3) - 1)
      }
      
      // Apply trend bias
      movement += trend * volatility * 0.3
    }

    return currentPrice * (1 + movement)
  }

  /**
   * Start live quote updates for a symbol
   */
  subscribeToQuotes(symbol: string, callback: (quote: TradingViewQuote) => void): void {
    // Initialize price if not exists
    if (!this.lastPrices.has(symbol)) {
      this.lastPrices.set(symbol, this.getInitialPrice(symbol))
      this.trendDirections.set(symbol, 0)
    }

    // Add callback
    if (!this.quoteCallbacks.has(symbol)) {
      this.quoteCallbacks.set(symbol, [])
    }
    this.quoteCallbacks.get(symbol)!.push(callback)

    // Start update interval if not already running
    if (!this.updateIntervals.has(symbol)) {
      const interval = setInterval(() => {
        this.generateQuoteUpdate(symbol)
      }, this.config.updateInterval)
      
      this.updateIntervals.set(symbol, interval)
    }

    if (this.config.enableLogging) {
      console.log(`📊 [MockTradingDataService] Started quote subscription for ${symbol}`)
    }
  }

  /**
   * Stop quote updates for a symbol
   */
  unsubscribeFromQuotes(symbol: string, callback?: Function): void {
    if (callback) {
      const callbacks = this.quoteCallbacks.get(symbol) || []
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.quoteCallbacks.delete(symbol)
    }

    // Stop interval if no callbacks remain
    const callbacks = this.quoteCallbacks.get(symbol) || []
    if (callbacks.length === 0) {
      const interval = this.updateIntervals.get(symbol)
      if (interval) {
        clearInterval(interval)
        this.updateIntervals.delete(symbol)
      }
    }

    if (this.config.enableLogging) {
      console.log(`📊 [MockTradingDataService] Stopped quote subscription for ${symbol}`)
    }
  }

  /**
   * Generate and broadcast quote update
   */
  private generateQuoteUpdate(symbol: string): void {
    const lastPrice = this.lastPrices.get(symbol) || this.getInitialPrice(symbol)
    const newPrice = this.generatePriceMovement(symbol, lastPrice)
    
    this.lastPrices.set(symbol, newPrice)

    const quote: TradingViewQuote = {
      symbol,
      price: newPrice,
      change: newPrice - lastPrice,
      changePercent: ((newPrice - lastPrice) / lastPrice) * 100,
      volume: Math.floor(Math.random() * 1000000) + 100000,
      timestamp: Math.floor(Date.now() / 1000)
    }

    // Send to all callbacks
    const callbacks = this.quoteCallbacks.get(symbol) || []
    callbacks.forEach(callback => {
      try {
        callback(quote)
      } catch (error) {
        console.warn('⚠️ [MockTradingDataService] Error in quote callback:', error)
      }
    })
  }

  /**
   * Generate historical candle data
   */
  generateHistoricalCandles(
    symbol: string, 
    timeframe: string, 
    count: number = 1000
  ): TradingViewCandle[] {
    const candles: TradingViewCandle[] = []
    const basePrice = this.getInitialPrice(symbol)
    const timeframeMs = this.getTimeframeMs(timeframe)
    const now = Date.now()
    
    let currentPrice = basePrice
    
    // Generate historical trend
    const historicalTrend = Math.floor(Math.random() * 3) - 1
    
    for (let i = count - 1; i >= 0; i--) {
      const time = Math.floor((now - (i * timeframeMs)) / 1000)
      
      // Generate OHLC data with more realistic patterns
      const open = currentPrice
      
      // Generate multiple price movements within the candle
      const movements = Math.floor(Math.random() * 5) + 1
      let high = open
      let low = open
      let close = open
      
      for (let j = 0; j < movements; j++) {
        let movement = this.config.priceVolatility * (Math.random() - 0.5) * 2
        
        // Apply historical trend bias
        if (this.config.enableTrends) {
          movement += historicalTrend * this.config.priceVolatility * 0.1
        }
        
        close = close * (1 + movement)
        high = Math.max(high, close)
        low = Math.min(low, close)
      }
      
      // Ensure realistic OHLC relationships
      high = Math.max(high, open, close)
      low = Math.min(low, open, close)
      
      const volume = Math.floor(Math.random() * 1000000) + 100000
      
      candles.push({
        time,
        open,
        high,
        low,
        close,
        volume
      })
      
      currentPrice = close
    }
    
    // Update last price for live data continuity
    this.lastPrices.set(symbol, currentPrice)
    
    if (this.config.enableLogging) {
      console.log(`📊 [MockTradingDataService] Generated ${candles.length} historical candles for ${symbol}`)
    }
    
    return candles
  }

  /**
   * Get timeframe in milliseconds
   */
  private getTimeframeMs(timeframe: string): number {
    const timeframeMap: { [key: string]: number } = {
      '1M': 60 * 1000,
      '5M': 5 * 60 * 1000,
      '15M': 15 * 60 * 1000,
      '1H': 60 * 60 * 1000,
      '4H': 4 * 60 * 60 * 1000,
      '1D': 24 * 60 * 60 * 1000,
      '1W': 7 * 24 * 60 * 60 * 1000,
      '1MONTH': 30 * 24 * 60 * 60 * 1000
    }

    return timeframeMap[timeframe] || 60 * 1000
  }

  /**
   * Get current price for a symbol
   */
  getCurrentPrice(symbol: string): number {
    return this.lastPrices.get(symbol) || this.getInitialPrice(symbol)
  }

  /**
   * Check if symbol is subscribed
   */
  isSubscribed(symbol: string): boolean {
    return this.updateIntervals.has(symbol)
  }

  /**
   * Get all subscribed symbols
   */
  getSubscribedSymbols(): string[] {
    return Array.from(this.updateIntervals.keys())
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    // Clear all intervals
    this.updateIntervals.forEach(interval => clearInterval(interval))
    
    // Clear all data
    this.updateIntervals.clear()
    this.quoteCallbacks.clear()
    this.candleCallbacks.clear()
    this.lastPrices.clear()
    this.trendDirections.clear()

    if (this.config.enableLogging) {
      console.log('📊 [MockTradingDataService] Cleaned up all subscriptions')
    }
  }
}

// Create singleton instance
let mockTradingDataService: MockTradingDataService | null = null

export const getMockTradingDataService = (): MockTradingDataService => {
  if (!mockTradingDataService) {
    mockTradingDataService = new MockTradingDataService()
  }
  return mockTradingDataService
}

export default MockTradingDataService