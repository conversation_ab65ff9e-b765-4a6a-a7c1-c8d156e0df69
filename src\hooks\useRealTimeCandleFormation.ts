'use client'

/**
 * 🕯️ Real-Time Candle Formation Hook
 * 
 * This hook manages real-time candle formation based on timeframe.
 * It processes live price updates and forms candles in real-time.
 */

import { useState, useEffect, useRef, useCallback } from 'react'

interface CandleData {
  time: string | number
  open: number
  high: number
  low: number
  close: number
}

interface PriceUpdate {
  symbol: string
  price: number
  timestamp: string
  bid?: number
  ask?: number
}

interface UseRealTimeCandleFormationOptions {
  symbol: string
  timeframe: string // '1m', '5m', '15m', '1H', '4H', '1D'
  onCandleUpdate?: (candle: CandleData) => void
  onNewCandle?: (candle: CandleData) => void
  initialCandles?: CandleData[]
}

interface CandleFormationHook {
  currentCandle: CandleData | null
  updatePrice: (update: PriceUpdate) => void
  reset: () => void
  getTimeframeSeconds: () => number
  getCurrentCandleTime: () => number
}

export const useRealTimeCandleFormation = (
  options: UseRealTimeCandleFormationOptions
): CandleFormationHook => {
  const { symbol, timeframe, onCandleUpdate, onNewCandle, initialCandles } = options
  
  const [currentCandle, setCurrentCandle] = useState<CandleData | null>(null)
  const lastPriceRef = useRef<number>(0)
  const candleStartTimeRef = useRef<number>(0)
  const isInitializedRef = useRef<boolean>(false)

  // Convert timeframe to seconds
  const getTimeframeSeconds = useCallback((): number => {
    const timeframeMap: Record<string, number> = {
      '1m': 60,
      '5m': 300,
      '15m': 900,
      '30m': 1800,
      '1H': 3600,
      '4H': 14400,
      '1D': 86400
    }
    return timeframeMap[timeframe] || 3600 // Default to 1H
  }, [timeframe])

  // Get current candle time aligned to timeframe
  const getCurrentCandleTime = useCallback((): number => {
    const now = Math.floor(Date.now() / 1000)
    const timeframeSeconds = getTimeframeSeconds()
    const alignedTime = Math.floor(now / timeframeSeconds) * timeframeSeconds
    
    // Validate the aligned time is reasonable
    if (alignedTime <= 0 || alignedTime > now + timeframeSeconds) {
      console.warn(`🕯️ Invalid aligned time calculated:`, {
        now,
        timeframeSeconds,
        alignedTime,
        nowDate: new Date(now * 1000).toISOString(),
        alignedDate: new Date(alignedTime * 1000).toISOString()
      })
      return now // Fallback to current time
    }
    
    return alignedTime
  }, [getTimeframeSeconds])

  // Initialize or check if we need a new candle
  const checkForNewCandle = useCallback((timestamp: number, price: number) => {
    const currentTime = getCurrentCandleTime()
    
    // If no current candle or time has moved to next candle period
    if (!currentCandle || candleStartTimeRef.current !== currentTime) {
      // If we had a previous candle, notify that it's completed
      if (currentCandle && onNewCandle) {
        onNewCandle({ ...currentCandle })
        console.log(`🕯️ Completed candle for ${symbol} at ${new Date(candleStartTimeRef.current * 1000).toISOString()}`)
      }
      
      // Start new candle
      const newCandle: CandleData = {
        time: currentTime,
        open: price,
        high: price,
        low: price,
        close: price
      }
      
      setCurrentCandle(newCandle)
      candleStartTimeRef.current = currentTime
      
      console.log(`🆕 Started new candle for ${symbol} at ${new Date(currentTime * 1000).toISOString()}`)
      
      if (onCandleUpdate) {
        onCandleUpdate(newCandle)
      }
      
      return true // New candle created
    }
    
    return false // Using existing candle
  }, [currentCandle, symbol, getCurrentCandleTime, onNewCandle, onCandleUpdate])

  // Update price and modify current candle
  const updatePrice = useCallback((update: PriceUpdate) => {
    if (update.symbol !== symbol) return
    
    const price = update.price
    
    // Validate price
    if (!isFinite(price) || price <= 0) {
      console.warn(`🕯️ Invalid price for ${symbol}:`, price)
      return
    }
    
    // Parse timestamp with validation
    let timestamp: number
    if (typeof update.timestamp === 'string') {
      timestamp = new Date(update.timestamp).getTime() / 1000
      if (isNaN(timestamp)) {
        console.warn(`🕯️ Invalid timestamp for ${symbol}:`, update.timestamp)
        return
      }
    } else if (typeof update.timestamp === 'number') {
      timestamp = update.timestamp
    } else {
      console.warn(`🕯️ Invalid timestamp type for ${symbol}:`, typeof update.timestamp)
      return
    }
    
    // Ensure timestamp is reasonable (not in far future or past)
    const now = Date.now() / 1000
    if (Math.abs(timestamp - now) > 86400) { // More than 24 hours difference
      console.warn(`🕯️ Timestamp too far from current time for ${symbol}:`, {
        timestamp,
        now,
        difference: Math.abs(timestamp - now),
        timestampDate: new Date(timestamp * 1000).toISOString(),
        nowDate: new Date(now * 1000).toISOString()
      })
      // Use current time instead of invalid timestamp
      timestamp = now
    }
    
    // Check if we need a new candle
    const isNewCandle = checkForNewCandle(timestamp, price)
    
    if (!isNewCandle && currentCandle) {
      // Update existing candle
      const updatedCandle: CandleData = {
        ...currentCandle,
        high: Math.max(currentCandle.high, price),
        low: Math.min(currentCandle.low, price),
        close: price
      }
      
      setCurrentCandle(updatedCandle)
      
      if (onCandleUpdate) {
        onCandleUpdate(updatedCandle)
      }
      
      // Only log significant price changes to avoid spam
      if (Math.abs(price - lastPriceRef.current) > (lastPriceRef.current * 0.0001)) {
        console.log(`📈 Updated candle for ${symbol}: O=${updatedCandle.open.toFixed(5)} H=${updatedCandle.high.toFixed(5)} L=${updatedCandle.low.toFixed(5)} C=${updatedCandle.close.toFixed(5)}`)
      }
    }
    
    lastPriceRef.current = price
  }, [symbol, currentCandle, checkForNewCandle, onCandleUpdate])

  // Reset candle formation
  const reset = useCallback(() => {
    setCurrentCandle(null)
    candleStartTimeRef.current = 0
    lastPriceRef.current = 0
    isInitializedRef.current = false
    console.log(`🔄 Reset candle formation for ${symbol}`)
  }, [symbol])

  // Initialize with historical data if provided
  useEffect(() => {
    if (initialCandles && initialCandles.length > 0 && !isInitializedRef.current) {
      const lastCandle = initialCandles[initialCandles.length - 1]
      const lastCandleTime = typeof lastCandle.time === 'string' 
        ? new Date(lastCandle.time).getTime() / 1000 
        : lastCandle.time
      
      // Check if the last historical candle is still current
      const currentTime = getCurrentCandleTime()
      
      if (Math.abs(currentTime - lastCandleTime) < getTimeframeSeconds()) {
        // Last historical candle is still current, use it as starting point
        setCurrentCandle({
          time: currentTime,
          open: lastCandle.open,
          high: lastCandle.high,
          low: lastCandle.low,
          close: lastCandle.close
        })
        candleStartTimeRef.current = currentTime
        lastPriceRef.current = lastCandle.close
        
        console.log(`🎯 Initialized with current candle from historical data for ${symbol}`)
      }
      
      isInitializedRef.current = true
    }
  }, [initialCandles, symbol, getCurrentCandleTime, getTimeframeSeconds])

  // Reset when symbol or timeframe changes
  useEffect(() => {
    reset()
  }, [symbol, timeframe, reset])

  return {
    currentCandle,
    updatePrice,
    reset,
    getTimeframeSeconds,
    getCurrentCandleTime
  }
}