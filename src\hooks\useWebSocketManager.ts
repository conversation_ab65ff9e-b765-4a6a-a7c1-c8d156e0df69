'use client'

/**
 * WebSocket Manager Hook
 * 
 * Provides a React hook interface for the comprehensive WebSocket error handling system.
 * Integrates WebSocketManager, FallbackDataPipeline, and error recovery.
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import WebSocketManager, { 
  ConnectionState, 
  WebSocketError, 
  WebSocketErrorType 
} from '@/lib/websocket/WebSocketManager'
import FallbackDataPipeline from '@/lib/websocket/FallbackDataPipeline'

export interface UseWebSocketManagerOptions {
  url: string
  protocols?: string[]
  reconnectAttempts?: number
  maxReconnectDelay?: number
  baseReconnectDelay?: number
  heartbeatInterval?: number
  connectionTimeout?: number
  enableFallback?: boolean
  autoConnect?: boolean
  onError?: (error: WebSocketError) => void
  onConnectionChange?: (state: ConnectionState) => void
  onMessage?: (message: any) => void
}

export interface WebSocketState {
  isConnected: boolean
  connectionState: ConnectionState
  isReconnecting: boolean
  isFallbackActive: boolean
  lastError: WebSocketError | null
  metrics: {
    totalConnections: number
    successfulConnections: number
    failedConnections: number
    totalReconnects: number
    avgConnectionTime: number
    uptime: number
  }
  reconnectInfo: {
    attempt: number
    maxAttempts: number
    nextRetryIn: number
  } | null
}

export interface WebSocketActions {
  connect: () => Promise<void>
  disconnect: () => void
  send: (message: any) => boolean
  retry: () => Promise<void>
  addSubscription: (subscription: string) => void
  removeSubscription: (subscription: string) => void
  clearError: () => void
}

export const useWebSocketManager = (
  options: UseWebSocketManagerOptions
): [WebSocketState, WebSocketActions] => {
  const wsManagerRef = useRef<WebSocketManager | null>(null)
  const fallbackPipelineRef = useRef<FallbackDataPipeline | null>(null)
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null)

  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    connectionState: ConnectionState.DISCONNECTED,
    isReconnecting: false,
    isFallbackActive: false,
    lastError: null,
    metrics: {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      totalReconnects: 0,
      avgConnectionTime: 0,
      uptime: 0
    },
    reconnectInfo: null
  })

  // Initialize WebSocket manager and fallback pipeline
  useEffect(() => {
    console.log('🔧 [useWebSocketManager] Initializing WebSocket manager')

    // Create WebSocket manager
    wsManagerRef.current = new WebSocketManager({
      url: options.url,
      protocols: options.protocols,
      reconnectAttempts: options.reconnectAttempts || 5,
      maxReconnectDelay: options.maxReconnectDelay || 30000,
      baseReconnectDelay: options.baseReconnectDelay || 1000,
      heartbeatInterval: options.heartbeatInterval || 30000,
      connectionTimeout: options.connectionTimeout || 10000,
      enableFallback: options.enableFallback !== false
    })

    // Create fallback pipeline
    fallbackPipelineRef.current = new FallbackDataPipeline({
      enableFallback: options.enableFallback !== false,
      mockDataEnabled: true
    })

    // Set up event handlers
    setupEventHandlers()

    // Auto-connect if requested
    if (options.autoConnect !== false) {
      connect()
    }

    return () => {
      cleanup()
    }
  }, [options.url])

  // Set up event handlers for WebSocket manager and fallback pipeline
  const setupEventHandlers = useCallback(() => {
    const wsManager = wsManagerRef.current
    const fallbackPipeline = fallbackPipelineRef.current

    if (!wsManager || !fallbackPipeline) return

    // WebSocket Manager Events
    wsManager.on('connected', handleConnected)
    wsManager.on('disconnected', handleDisconnected)
    wsManager.on('message', handleMessage)
    wsManager.on('error', handleError)
    wsManager.on('reconnecting', handleReconnecting)
    wsManager.on('max_reconnects_reached', handleMaxReconnectsReached)
    wsManager.on('state_change', handleStateChange)

    // Fallback Pipeline Events
    fallbackPipeline.on('fallback_activated', handleFallbackActivated)
    fallbackPipeline.on('fallback_deactivated', handleFallbackDeactivated)

  }, [])

  // Event Handlers
  const handleConnected = useCallback(() => {
    console.log('✅ [useWebSocketManager] Connected')
    
    fallbackPipelineRef.current?.setDataSourceAvailable('websocket', true)
    
    setState(prev => ({
      ...prev,
      isConnected: true,
      connectionState: ConnectionState.CONNECTED,
      isReconnecting: false,
      reconnectInfo: null,
      metrics: wsManagerRef.current?.getMetrics() || prev.metrics
    }))

    options.onConnectionChange?.(ConnectionState.CONNECTED)
  }, [options.onConnectionChange])

  const handleDisconnected = useCallback((error?: WebSocketError) => {
    console.log('❌ [useWebSocketManager] Disconnected:', error?.message)
    
    fallbackPipelineRef.current?.setDataSourceAvailable('websocket', false)
    
    setState(prev => ({
      ...prev,
      isConnected: false,
      connectionState: ConnectionState.DISCONNECTED,
      isReconnecting: false,
      lastError: error || null,
      metrics: wsManagerRef.current?.getMetrics() || prev.metrics
    }))

    options.onConnectionChange?.(ConnectionState.DISCONNECTED)
  }, [options.onConnectionChange])

  const handleMessage = useCallback((message: any) => {
    console.log('📨 [useWebSocketManager] Message received:', message?.type)
    
    // Cache message data for fallback use
    if (message.type === 'quote' && message.symbol) {
      fallbackPipelineRef.current?.cacheData(`${message.symbol}_quote`, message.data)
    }
    
    options.onMessage?.(message)
  }, [options.onMessage])

  const handleError = useCallback((error: WebSocketError) => {
    console.error('❌ [useWebSocketManager] Error:', error)
    
    setState(prev => ({
      ...prev,
      lastError: error,
      metrics: wsManagerRef.current?.getMetrics() || prev.metrics
    }))

    options.onError?.(error)
  }, [options.onError])

  const handleReconnecting = useCallback((info: { attempt: number; maxAttempts: number; delay: number }) => {
    console.log('🔄 [useWebSocketManager] Reconnecting:', info)
    
    setState(prev => ({
      ...prev,
      isReconnecting: true,
      connectionState: ConnectionState.RECONNECTING,
      reconnectInfo: {
        attempt: info.attempt,
        maxAttempts: info.maxAttempts,
        nextRetryIn: info.delay
      }
    }))

    // Update countdown timer
    if (reconnectTimerRef.current) {
      clearInterval(reconnectTimerRef.current)
    }

    let remainingTime = Math.ceil(info.delay / 1000)
    reconnectTimerRef.current = setInterval(() => {
      remainingTime--
      setState(prev => ({
        ...prev,
        reconnectInfo: prev.reconnectInfo ? {
          ...prev.reconnectInfo,
          nextRetryIn: remainingTime * 1000
        } : null
      }))

      if (remainingTime <= 0) {
        if (reconnectTimerRef.current) {
          clearInterval(reconnectTimerRef.current)
          reconnectTimerRef.current = null
        }
      }
    }, 1000)

    options.onConnectionChange?.(ConnectionState.RECONNECTING)
  }, [options.onConnectionChange])

  const handleMaxReconnectsReached = useCallback(() => {
    console.error('❌ [useWebSocketManager] Max reconnection attempts reached')
    
    setState(prev => ({
      ...prev,
      isReconnecting: false,
      connectionState: ConnectionState.FAILED,
      reconnectInfo: null
    }))

    if (reconnectTimerRef.current) {
      clearInterval(reconnectTimerRef.current)
      reconnectTimerRef.current = null
    }

    options.onConnectionChange?.(ConnectionState.FAILED)
  }, [options.onConnectionChange])

  const handleStateChange = useCallback((change: { from: ConnectionState; to: ConnectionState }) => {
    console.log('🔄 [useWebSocketManager] State change:', change)
    
    setState(prev => ({
      ...prev,
      connectionState: change.to,
      metrics: wsManagerRef.current?.getMetrics() || prev.metrics
    }))
  }, [])

  const handleFallbackActivated = useCallback(() => {
    console.log('🔄 [useWebSocketManager] Fallback activated')
    
    setState(prev => ({
      ...prev,
      isFallbackActive: true
    }))
  }, [])

  const handleFallbackDeactivated = useCallback(() => {
    console.log('✅ [useWebSocketManager] Fallback deactivated')
    
    setState(prev => ({
      ...prev,
      isFallbackActive: false
    }))
  }, [])

  // Actions
  const connect = useCallback(async (): Promise<void> => {
    if (!wsManagerRef.current) {
      throw new Error('WebSocket manager not initialized')
    }

    try {
      console.log('🔌 [useWebSocketManager] Connecting...')
      await wsManagerRef.current.connect()
    } catch (error) {
      console.error('❌ [useWebSocketManager] Connection failed:', error)
      throw error
    }
  }, [])

  const disconnect = useCallback((): void => {
    console.log('📊 [useWebSocketManager] Disconnecting...')
    wsManagerRef.current?.disconnect()
  }, [])

  const send = useCallback((message: any): boolean => {
    if (!wsManagerRef.current) {
      console.warn('⚠️ [useWebSocketManager] Cannot send - manager not initialized')
      return false
    }

    return wsManagerRef.current.send(message)
  }, [])

  const retry = useCallback(async (): Promise<void> => {
    console.log('🔄 [useWebSocketManager] Manual retry requested')
    
    // Clear any existing error
    setState(prev => ({
      ...prev,
      lastError: null
    }))

    // Disconnect and reconnect
    disconnect()
    
    // Wait a moment before reconnecting
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return connect()
  }, [connect, disconnect])

  const addSubscription = useCallback((subscription: string): void => {
    wsManagerRef.current?.addSubscription(subscription)
  }, [])

  const removeSubscription = useCallback((subscription: string): void => {
    wsManagerRef.current?.removeSubscription(subscription)
  }, [])

  const clearError = useCallback((): void => {
    setState(prev => ({
      ...prev,
      lastError: null
    }))
  }, [])

  // Cleanup function
  const cleanup = useCallback(() => {
    console.log('🧹 [useWebSocketManager] Cleaning up...')
    
    if (reconnectTimerRef.current) {
      clearInterval(reconnectTimerRef.current)
      reconnectTimerRef.current = null
    }

    wsManagerRef.current?.destroy()
    wsManagerRef.current = null
    fallbackPipelineRef.current = null
  }, [])

  // Update metrics periodically
  useEffect(() => {
    const metricsInterval = setInterval(() => {
      const metrics = wsManagerRef.current?.getMetrics()
      if (metrics) {
        setState(prev => ({ ...prev, metrics }))
      }
    }, 1000)

    return () => clearInterval(metricsInterval)
  }, [])

  const actions: WebSocketActions = {
    connect,
    disconnect,
    send,
    retry,
    addSubscription,
    removeSubscription,
    clearError
  }

  return [state, actions]
}

/**
 * Simplified hook for basic WebSocket usage
 */
export const useWebSocket = (
  url: string,
  options: Partial<UseWebSocketManagerOptions> = {}
) => {
  const [state, actions] = useWebSocketManager({
    url,
    ...options
  })

  return {
    isConnected: state.isConnected,
    connectionState: state.connectionState,
    lastError: state.lastError,
    isReconnecting: state.isReconnecting,
    send: actions.send,
    retry: actions.retry
  }
}